#include "sprm.h"
#include <QApplication>
#include <typeinfo>

#include "deviceFactory.h"
#include "deviceInstance.h"

#include "loadXml.h"
#include "sprmReadIni.h"

#include "client_besterP.h"
#include "model_pmsCurveP.h"
#include "model_simpleXorP.h"

#include "serialThreadNode.h"

#include "myConnSql.h"

#include "typeConvert.h"

#include "qLog.h"

namespace proximity_sensor {

const QMetaEnum CSprm::s_system_mode_Enum = QMetaEnum::fromType<CSprm::ESysMode>();
const QMetaEnum CSprm::s_led_mode_Enum    = QMetaEnum::fromType<CSprm::ELedMode>();

// const QMap<CSprm::ESysMode, IProximitySensor::EUniversalTasks> CSprm::s_systemMode_tasks_map = {
//     {ESysMode::eTEST_MODE, EUniversalTasks::eTASK_TEST_MODE},
//     {ESysMode::eHISTOGRAM_MODE, EUniversalTasks::eTASK_ANALYSIS_MODE},
//     {ESysMode::eCALIBRATION_MODE, EUniversalTasks::eTASK_CALIB_MODE},
//     {ESysMode::eOPTIMIZATION_MODE, EUniversalTasks::eTASK_OPTIMIZE_MODE},
//     {ESysMode::eCONTINUE_RANGE_MODE, EUniversalTasks::eTASK_VERIFY_MODE},
//     {ESysMode::eSINGLE_RANGE_MODE, EUniversalTasks::eTASK_VERIFY_MODE},
//     {ESysMode::eSINGLE_HIST_MIX_MODE, EUniversalTasks::eTASK_VERIFY_MODE},
//     {ESysMode::eSINGLE_HIST_MIX_MODE, EUniversalTasks::eTASK_VERIFY_MODE},
//     {ESysMode::eSTANDBY_MODE, EUniversalTasks::eTASK_STANDBY_MODE},
//     {ESysMode::eWORK_MODE, EUniversalTasks::eTASK_WORK_MODE},
// };


const QMap<QString, QMap<CSprm::ESysMode, uint16_t>> &CSprm::getCmdSystemMode() {
    static const QMap<QString, QMap<CSprm::ESysMode, uint16_t>> instance = {
        // EPmsAllId
        {"A1_old_system_mode",
         {
             {ESysMode::eTEST_MODE, 0},
             {ESysMode::eHISTOGRAM_MODE, 0xff},
             {ESysMode::eCALIBRATION_MODE, 2},
             {ESysMode::eOPTIMIZATION_MODE, 5},
             {ESysMode::eCONTINUE_RANGE_MODE, 0xff},
             {ESysMode::eSINGLE_RANGE_MODE, 1},
             {ESysMode::eSINGLE_HIST_MIX_MODE, 0xff},
             {ESysMode::eSTANDBY_MODE, 4},
             {ESysMode::eWORK_MODE, 3},
         }},
        {"A2_standard_system_mode",
         {
             {ESysMode::eTEST_MODE, 0xA5F1},
             {ESysMode::eHISTOGRAM_MODE, 0xA5F2},
             {ESysMode::eCALIBRATION_MODE, 0xA5F3},
             {ESysMode::eOPTIMIZATION_MODE, 0xA5F4},
             {ESysMode::eCONTINUE_RANGE_MODE, 0xA5F5},
             {ESysMode::eSINGLE_RANGE_MODE, 0xA5F6},
             {ESysMode::eSINGLE_HIST_MIX_MODE, 0xA5F7},
             {ESysMode::eSTANDBY_MODE, 0xffff},
             {ESysMode::eWORK_MODE, 0xA5F8},
         }},
    };

    return instance;
};

/**
 * @brief 获取系统模式
 *
 * @param system_proj
 * @return const QMap<QString, uint16_t>&
 */
const QMap<QString, uint16_t> &CSprm::getTrCmdSystemMode(const QString &system_proj) {
    auto system_mode_list = getCmdSystemMode()[system_proj];

    // system mode
    static QMap<QString, uint16_t> tr_system_mode_list;
    for (auto it = system_mode_list.begin(); it != system_mode_list.end(); it++) {
        tr_system_mode_list.insert(CTypeConvert::enumToString(s_system_mode_Enum, uint16_t(it.key())), it.value());
    }

    return tr_system_mode_list;
};


const QMap<QString, QMap<CSprm::ELedMode, uint16_t>> &CSprm::getCmdLedMode() {
    static const QMap<QString, QMap<CSprm::ELedMode, uint16_t>> instance = {
        // EPmsAllId
        {"default",
         {
             {ELedMode::eCLOSE, 0},
             {ELedMode::eOPEN, 1},
             {ELedMode::eBREATH, 3},
             {ELedMode::eFLICKER, 4},
         }},
        {"A1_old_led_mode",
         {
             {ELedMode::eCLOSE, 1},
             {ELedMode::eOPEN, 2},
             {ELedMode::eBREATH, 3},
             {ELedMode::eFLICKER, 4},
         }},
    };

    return instance;
}

const QMap<QString, uint16_t> &CSprm::getTrCmdLedMode(const QString &led_mode_type) {
    auto led_mode_list = getCmdLedMode()[led_mode_type];

    // led mode
    static QMap<QString, uint16_t> tr_led_mode_list;
    for (auto it = led_mode_list.begin(); it != led_mode_list.end(); it++) {
        tr_led_mode_list.insert(CTypeConvert::enumToString(s_led_mode_Enum, uint16_t(it.key())), it.value());
    }

    return tr_led_mode_list;
}

const QMap<QString, QMap<uint16_t, IProtocol::StDataAreas>> &CSprm::getPmsIdList() {
    static const QMap<QString, QMap<uint16_t, IProtocol::StDataAreas>> instance = {
        // EPmsAllId
        {"A1_standard",
         {
             {static_cast<uint16_t>(EPmsAllId::eCALIB1), IProtocol::StDataAreas{.mark = 0x01, .type = (uint16_t)IBaseProtocol::ECmdType::eCTRL, .options = {}}},
             {(uint16_t)EPmsAllId::eCALIB2, {0x02, (uint16_t)IBaseProtocol::ECmdType::eCTRL, {}}},
             //  {(uint16_t)EPmsAllId::eSET_RANGE, {0x04, (uint16_t)IBaseProtocol::ECmdType::eWRITE, {"", 60}}},
             {(uint16_t)EPmsAllId::eSELF_ADAPT, {0x05, (uint16_t)IBaseProtocol::ECmdType::eCTRL, {}}},
             {(uint16_t)EPmsAllId::eLED_IO, {0x06, (uint16_t)IBaseProtocol::ECmdType::eWRITE, getTrCmdLedMode("A1_old_led_mode")}},
             {(uint16_t)EPmsAllId::ePROC_MODE, {0x07, (uint16_t)IBaseProtocol::ECmdType::eWRITE, getTrCmdSystemMode("A1_old_system_mode")}},
             {(uint16_t)EPmsAllId::eDATA_MODE, {0x08, (uint16_t)IBaseProtocol::ECmdType::eWRITE, {}}},
             {(uint16_t)EPmsAllId::eQUERY_DIST, {0x0A, (uint16_t)IBaseProtocol::ECmdType::eCTRL, {}}},
             {(uint16_t)EPmsAllId::eVERSION, {0x0B, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
             {(uint16_t)EPmsAllId::ePARAM, {0x0C, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
             {(uint16_t)EPmsAllId::eCHIP_ID, {0x0D, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
             {(uint16_t)EPmsAllId::eDDS_LOG, {0x0E, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
         }},
        {"client_bester",
         {
             {(uint16_t)EPmsAllId::eQUERY_DIST, {0x00, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
             {(uint16_t)EPmsAllId::eSET_RANGE, {0x01, (uint16_t)IBaseProtocol::ECmdType::eWRITE, {}}},
             {(uint16_t)EPmsAllId::eCALIB1, {0x02, (uint16_t)IBaseProtocol::ECmdType::eCTRL, {}}},
             {(uint16_t)EPmsAllId::eCALIB2, {0x03, (uint16_t)IBaseProtocol::ECmdType::eCTRL, {}}},
             {(uint16_t)EPmsAllId::ePROC_MODE, {0x07, (uint16_t)IBaseProtocol::ECmdType::eWRITE, {}}},
             {(uint16_t)EPmsAllId::eDATA_MODE, {0x08, (uint16_t)IBaseProtocol::ECmdType::eWRITE, {}}},
             {(uint16_t)EPmsAllId::eEXPAND_DIST, {0x0A, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
             {(uint16_t)EPmsAllId::ePARAM, {0x0C, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
             {(uint16_t)EPmsAllId::eCHIP_ID, {0x0D, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
             {(uint16_t)EPmsAllId::eVERSION, {0x0B, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
             {(uint16_t)EPmsAllId::eCUSTOM_VERSION, {0xBB, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
             {(uint16_t)EPmsAllId::eDDS_LOG, {0x0E, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
         }},
        {"client_HELI",
         {{(uint16_t)EPmsAllId::eCALIB1, {0x01, (uint16_t)IBaseProtocol::ECmdType::eCTRL, {}}},
          {(uint16_t)EPmsAllId::eCALIB2, {0x02, (uint16_t)IBaseProtocol::ECmdType::eCTRL, {}}},
          {(uint16_t)EPmsAllId::eSET_RANGE, {0x04, (uint16_t)IBaseProtocol::ECmdType::eWRITE, {}}},
          {(uint16_t)EPmsAllId::eSELF_ADAPT, {0x05, (uint16_t)IBaseProtocol::ECmdType::eCTRL, {}}},
          {(uint16_t)EPmsAllId::eLED_IO, {0x06, (uint16_t)IBaseProtocol::ECmdType::eWRITE, {}}},
          {(uint16_t)EPmsAllId::ePROC_MODE, {0x07, (uint16_t)IBaseProtocol::ECmdType::eWRITE, {}}},
          {(uint16_t)EPmsAllId::eDATA_MODE, {0x08, (uint16_t)IBaseProtocol::ECmdType::eWRITE, {}}},
          {(uint16_t)EPmsAllId::eQUERY_DIST, {0x0A, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
          {(uint16_t)EPmsAllId::eVERSION, {0x0B, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
          {(uint16_t)EPmsAllId::ePARAM, {0x0C, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
          {(uint16_t)EPmsAllId::eCHIP_ID, {0x0D, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
          {(uint16_t)EPmsAllId::eDDS_LOG, {0x0E, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
          {(uint16_t)EPmsAllId::eCUSTOM_VERSION, {0xFA, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}}}},
        {"client_YS",
         {{(uint16_t)EPmsAllId::eCALIB1, {0x01, (uint16_t)IBaseProtocol::ECmdType::eCTRL, {}}},
          {(uint16_t)EPmsAllId::eCALIB2, {0x02, (uint16_t)IBaseProtocol::ECmdType::eCTRL, {}}},
          {(uint16_t)EPmsAllId::eLED_IO, {0x06, (uint16_t)IBaseProtocol::ECmdType::eWRITE, {}}},
          {(uint16_t)EPmsAllId::ePROC_MODE, {0x07, (uint16_t)IBaseProtocol::ECmdType::eWRITE, {}}},
          {(uint16_t)EPmsAllId::eDATA_MODE, {0x08, (uint16_t)IBaseProtocol::ECmdType::eWRITE, {}}},
          {(uint16_t)EPmsAllId::eQUERY_DIST, {0x0A, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
          {(uint16_t)EPmsAllId::eVERSION, {0x0B, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
          {(uint16_t)EPmsAllId::ePARAM, {0x0C, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
          {(uint16_t)EPmsAllId::eCHIP_ID, {0x0D, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
          {(uint16_t)EPmsAllId::eDDS_LOG, {0x0E, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}}}},
        {"A2_standard",
         {{(uint16_t)EPmsAllId::eCHIP_ID, {0x01, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
          {(uint16_t)EPmsAllId::eVERSION, {0x02, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
          {(uint16_t)EPmsAllId::ePROC_MODE, {0x05, (uint16_t)IBaseProtocol::ECmdType::eREAD | (uint16_t)IBaseProtocol::ECmdType::eWRITE, {}}},
          {(uint16_t)EPmsAllId::eLED_IO, {0x06, (uint16_t)IBaseProtocol::ECmdType::eWRITE, {}}},
          {(uint16_t)EPmsAllId::eCALIB_ITEM, {0x07, (uint16_t)IBaseProtocol::ECmdType::eWRITE, {}}},
          {(uint16_t)EPmsAllId::eCOM_BUAD, {0x08, (uint16_t)IBaseProtocol::ECmdType::eWRITE, {}}},
          {(uint16_t)EPmsAllId::ePARAM, {0x09, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
          {(uint16_t)EPmsAllId::eQUERY_DIST, {0x0A, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}},
          {(uint16_t)EPmsAllId::eINTEGRAL_CNT, {0x0B, (uint16_t)IBaseProtocol::ECmdType::eREAD | (uint16_t)IBaseProtocol::ECmdType::eWRITE, {}}},
          {(uint16_t)EPmsAllId::eSAMPLE_VOL_VARS, {0x0C, (uint16_t)IBaseProtocol::ECmdType::eREAD | (uint16_t)IBaseProtocol::ECmdType::eWRITE, {}}},
          {(uint16_t)EPmsAllId::eDDS_LOG, {0x0E, (uint16_t)IBaseProtocol::ECmdType::eREAD, {}}}}},
    };
    return instance;
};

//***************************************task *******************/
const QMap<CSprm::EMassProductionTask, deviceProc::StDevTaskInfo> CSprm::sm_mass_production_tasks_name = {
    {CSprm::EMassProductionTask::eTASK_INTERNAL_VOL, {(uint16_t)CSprm::EMassProductionTask::eTASK_INTERNAL_VOL, "task_internal_vol", "", 0}},
    {CSprm::EMassProductionTask::eTASK_LED_CHECK, {(uint16_t)CSprm::EMassProductionTask::eTASK_LED_CHECK, "task_led_check", "", 0}},
    {CSprm::EMassProductionTask::eTASK_WORK_CURRENT, {(uint16_t)CSprm::EMassProductionTask::eTASK_WORK_CURRENT, "task_work_current", "", 0}},
    {CSprm::EMassProductionTask::eTASK_EXTERNAL_VOL, {(uint16_t)CSprm::EMassProductionTask::eTASK_EXTERNAL_VOL, "task_external_vol", "", 0}},
    {CSprm::EMassProductionTask::eTASK_TRIGGER, {(uint16_t)CSprm::EMassProductionTask::eTASK_TRIGGER, "task_trigger", "", 0}},
};

//* 不同项目配置初始化
const NProcessTemplateC::StTaskConfig
    CSprm::all_deviceTaskConfig_task_list[(uint16_t)EMassProductionType::eNOVA_A3 + 1][(uint16_t)EMassProductionTask::eTASK_LAST] = {
        {
            //
            {true, false, 100, 0, 50, 5},   // sm_basic_tasks_name[ECommDecBasicTasks::eTASK_VERSION],
            {true, false, 0, 0, 50, 5},     // sm_basic_tasks_name[ECommDecBasicTasks::eTASK_CHIP_ID],
            {true, false, 2000, 0, 50, 5},  // sm_basic_tasks_name[ECommDecBasicTasks::eTASK_READ_DDS],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EUniversalTasks::eTASK_SYSTEM_MODE]
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EUniversalTasks::eTASK_CALIB],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EUniversalTasks::eTASK_GET_DATA]
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EUniversalTasks::eTASK_OPTIMIZE],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EUniversalTasks::eTASK_WORK],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EMassProductionTask::eTASK_INTERNAL_VOL],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EMassProductionTask::eTASK_LED_CHECK],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EMassProductionTask::eTASK_WORK_CURRENT],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EMassProductionTask::eTASK_EXTERNAL_VOL],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EMassProductionTask::eTASK_TRIGGER],
        },
        {
            //
            {true, false, 100, 0, 50, 5},   // sm_basic_tasks_name[ECommDecBasicTasks::eTASK_VERSION],
            {true, false, 0, 0, 50, 5},     // sm_basic_tasks_name[ECommDecBasicTasks::eTASK_CHIP_ID],
            {true, false, 2000, 0, 50, 5},  // sm_basic_tasks_name[ECommDecBasicTasks::eTASK_READ_DDS],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EUniversalTasks::eTASK_SYSTEM_MODE]
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EUniversalTasks::eTASK_CALIB],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EUniversalTasks::eTASK_GET_DATA]
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EUniversalTasks::eTASK_OPTIMIZE],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EUniversalTasks::eTASK_WORK],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EMassProductionTask::eTASK_INTERNAL_VOL],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EMassProductionTask::eTASK_LED_CHECK],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EMassProductionTask::eTASK_WORK_CURRENT],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EMassProductionTask::eTASK_EXTERNAL_VOL],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EMassProductionTask::eTASK_TRIGGER],
        },
        {
            //
            {true, false, 100, 0, 50, 5},   // sm_basic_tasks_name[ECommDecBasicTasks::eTASK_VERSION],
            {true, false, 0, 0, 50, 5},     // sm_basic_tasks_name[ECommDecBasicTasks::eTASK_CHIP_ID],
            {true, false, 2000, 0, 50, 5},  // sm_basic_tasks_name[ECommDecBasicTasks::eTASK_READ_DDS],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EUniversalTasks::eTASK_SYSTEM_MODE]
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EUniversalTasks::eTASK_CALIB],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EUniversalTasks::eTASK_GET_DATA]
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EUniversalTasks::eTASK_OPTIMIZE],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EUniversalTasks::eTASK_WORK],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EMassProductionTask::eTASK_INTERNAL_VOL],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EMassProductionTask::eTASK_LED_CHECK],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EMassProductionTask::eTASK_WORK_CURRENT],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EMassProductionTask::eTASK_EXTERNAL_VOL],
            {true, false, 2000, 0, 50, 5},  // sm_universal_tasks_name[EMassProductionTask::eTASK_TRIGGER],

        },
};

/**
 * @brief: 生产涉及全部任务
 * "task_internal_vol": MCU内部采集电压
 * "task_external_vol"
 */
const QVector<QVector<NProcessTemplateC::StTaskConfig>> &CSprm::getDeviceTaskConfigProcessTypes() {
    static const QVector<QVector<NProcessTemplateC::StTaskConfig>> deviceTaskConfig_process_types = {
        // static
        //            [eNOVA_A1] =
        {
            all_deviceTaskConfig_task_list[(uint16_t)eNOVA_A1][(uint16_t)ECommDecBasicTasks::eTASK_READ_DDS],
            all_deviceTaskConfig_task_list[(uint16_t)eNOVA_A1][(uint16_t)ECommDecBasicTasks::eTASK_VERSION],
            all_deviceTaskConfig_task_list[(uint16_t)eNOVA_A1][(uint16_t)ECommDecBasicTasks::eTASK_CHIP_ID],
            all_deviceTaskConfig_task_list[(uint16_t)eNOVA_A1][(uint16_t)EUniversalTasks::eTASK_SYSTEM_MODE],
            all_deviceTaskConfig_task_list[(uint16_t)eNOVA_A1][(uint16_t)EUniversalTasks::eTASK_CALIB],
            all_deviceTaskConfig_task_list[(uint16_t)eNOVA_A1][(uint16_t)EUniversalTasks::eTASK_GET_DATA],
            all_deviceTaskConfig_task_list[(uint16_t)eNOVA_A1][(uint16_t)EUniversalTasks::eTASK_WORK],
            //            all_deviceTaskConfig_task_list[eNOVA_A1][eTASK_LED_CHECK],
            //            all_deviceTaskConfig_task_list[eNOVA_A1][eTASK_EXTERNAL_VOL],
            //            all_deviceTaskConfig_task_list[eNOVA_A1][eTASK_WORK_CURRENT],
        },
        //    [eNOVA_A2] =
        {
            all_deviceTaskConfig_task_list[(uint16_t)eNOVA_A2][(uint16_t)ECommDecBasicTasks::eTASK_READ_DDS],
            all_deviceTaskConfig_task_list[(uint16_t)eNOVA_A2][(uint16_t)ECommDecBasicTasks::eTASK_VERSION],
            all_deviceTaskConfig_task_list[(uint16_t)eNOVA_A2][(uint16_t)ECommDecBasicTasks::eTASK_CHIP_ID],
            all_deviceTaskConfig_task_list[(uint16_t)eNOVA_A2][(uint16_t)EUniversalTasks::eTASK_SYSTEM_MODE],
            all_deviceTaskConfig_task_list[(uint16_t)eNOVA_A2][(uint16_t)EUniversalTasks::eTASK_CALIB],
            all_deviceTaskConfig_task_list[(uint16_t)eNOVA_A2][(uint16_t)EUniversalTasks::eTASK_GET_DATA],
            all_deviceTaskConfig_task_list[(uint16_t)eNOVA_A2][(uint16_t)EUniversalTasks::eTASK_WORK],
        },
        //    [eNOVA_A3] =
        {
            all_deviceTaskConfig_task_list[(uint16_t)eNOVA_A3][(uint16_t)ECommDecBasicTasks::eTASK_READ_DDS],
            all_deviceTaskConfig_task_list[(uint16_t)eNOVA_A3][(uint16_t)ECommDecBasicTasks::eTASK_VERSION],
            all_deviceTaskConfig_task_list[(uint16_t)eNOVA_A3][(uint16_t)ECommDecBasicTasks::eTASK_CHIP_ID],
            all_deviceTaskConfig_task_list[(uint16_t)eNOVA_A3][(uint16_t)EUniversalTasks::eTASK_SYSTEM_MODE],
            all_deviceTaskConfig_task_list[(uint16_t)eNOVA_A3][(uint16_t)EUniversalTasks::eTASK_CALIB],
            all_deviceTaskConfig_task_list[(uint16_t)eNOVA_A3][(uint16_t)EUniversalTasks::eTASK_GET_DATA],
            all_deviceTaskConfig_task_list[(uint16_t)eNOVA_A3][(uint16_t)EUniversalTasks::eTASK_WORK],
        },
    };
    return deviceTaskConfig_process_types;
};

// const QMap<QString, ICommDevice::StCommStaticProperty> CSprm::sst_dev_property_list = {
//     // {{"Nova-A1", IDevice::eSENSOR_RANGE, true, {}, proximity_sensor::deviceTaskConfig_process_types.at(proximity_sensor::eNOVA_A1)},
//     //  {IPort::eUART, 19200, CProtocolFactory::eMODEL_SIMPLE_CHECKSUM_P, sm_pms_id_list["A1_standard"]}},
//     // {{"Nova-A1B", IDevice::eSENSOR_RANGE, true, {}, proximity_sensor::deviceTaskConfig_process_types.at(proximity_sensor::eNOVA_A1)},
//     //  {IPort::eUART, 19200, CProtocolFactory::eCLIENT_BESTER_P, sm_pms_id_list["client_bester"]}},
//     // {{"Nova-A1C", IDevice::eSENSOR_RANGE, true, {}, proximity_sensor::deviceTaskConfig_process_types.at(proximity_sensor::eNOVA_A1)},
//     //  {IPort::eUART, 19200, CProtocolFactory::eCLIENT_BESTER_P, sm_pms_id_list["client_bester"]}},
//     // {{"Nova-A1D", IDevice::eSENSOR_RANGE, true, {}, proximity_sensor::deviceTaskConfig_process_types.at(proximity_sensor::eNOVA_A1)},
//     //  {IPort::eUART, 19200, CProtocolFactory::eCLIENT_HELI_P, sm_pms_id_list["client_HELI"]}},
//     // {{"Nova-A1H", IDevice::eSENSOR_RANGE, true, {}, proximity_sensor::deviceTaskConfig_process_types.at(proximity_sensor::eNOVA_A1)},
//     //  {IPort::eUART, 19200, CProtocolFactory::eMODEL_SIMPLE_CHECKSUM_P, sm_pms_id_list["A1_standard"]}},
//     // {{"Nova-A1K", IDevice::eSENSOR_RANGE, true, {}, proximity_sensor::deviceTaskConfig_process_types.at(proximity_sensor::eNOVA_A1)},
//     //  {IPort::eUART, 19200, CProtocolFactory::eMODEL_SIMPLE_CHECKSUM_P, sm_pms_id_list["A1_standard"]}},
//     // {{"Nova-A1M", IDevice::eSENSOR_RANGE, true, {}, proximity_sensor::deviceTaskConfig_process_types.at(proximity_sensor::eNOVA_A1)},
//     //  {IPort::eUART, 115200, CProtocolFactory::eCLIENT_YOUSHENG_P, sm_pms_id_list["client_YS"]}},
//     // {{"Nova-A1L", IDevice::eSENSOR_RANGE, true, {}, proximity_sensor::deviceTaskConfig_process_types.at(proximity_sensor::eNOVA_A1)},
//     //  {IPort::eUART, 19200, CProtocolFactory::eMODEL_SIMPLE_CHECKSUM_P, sm_pms_id_list["A1_standard"]}},
//     // {{"Nova-A1P", IDevice::eSENSOR_RANGE, true, {}, proximity_sensor::deviceTaskConfig_process_types.at(proximity_sensor::eNOVA_A1)},
//     //  {IPort::eUART, 19200, CProtocolFactory::eCLIENT_BESTER_P, sm_pms_id_list["client_bester"]}},
//     // {{"Nova-A1TL", IDevice::eSENSOR_RANGE, true, {}, proximity_sensor::deviceTaskConfig_process_types.at(proximity_sensor::eNOVA_A1)},
//     //  {IPort::eUART, 19200, CProtocolFactory::eMODEL_SIMPLE_CHECKSUM_P, sm_pms_id_list["A1_standard"]}},
//     // {{"Nova-A2", IDevice::eSENSOR_RANGE, true, {}, proximity_sensor::deviceTaskConfig_process_types.at(proximity_sensor::eNOVA_A2)},
//     //  {IPort::eUART, 115200, CProtocolFactory::eMODEL_SIMPLE_XOR_P, sm_pms_id_list["A2_standard"]}},
//     // {{"Nova-A3", IDevice::eSENSOR_RANGE, true, {}, proximity_sensor::deviceTaskConfig_process_types.at(proximity_sensor::eNOVA_A3)},
//     //  {IPort::eUART, 115200, CProtocolFactory::eMODEL_SIMPLE_XOR_P, sm_pms_id_list["A2_standard"]}},

//     // {"Nova-A1", true, 19200, pms_id_list["A1_standard"], deviceTaskConfig_process_types[eNOVA_A1]},
//     // {"Nova-A1B", true, 19200, pms_id_list["client_bester"], deviceTaskConfig_process_types[eNOVA_A1]},
//     // {"Nova-A1C", true, 19200, pms_id_list["client_bester"], deviceTaskConfig_process_types[eNOVA_A1]},
//     // {"Nova-A1D", true, 19200, pms_id_list["client_HELI"], deviceTaskConfig_process_types[eNOVA_A1]},
//     // {"Nova-A1H", true, 19200, pms_id_list["A1_standard"], deviceTaskConfig_process_types[eNOVA_A1]},
//     // {"Nova-A1K", true, 19200, pms_id_list["A1_standard"], deviceTaskConfig_process_types[eNOVA_A1]},
//     // {"Nova-A1M", true, 115200, pms_id_list["client_YS"], deviceTaskConfig_process_types[eNOVA_A1]},
//     // {"Nova-A1L", true, 19200, pms_id_list["A1_standard"], deviceTaskConfig_process_types[eNOVA_A1]},
//     // {"Nova-A1P", true, 19200, pms_id_list["client_bester"], deviceTaskConfig_process_types[eNOVA_A1]},
//     // {"Nova-A1TL", true, 19200, pms_id_list["A1_standard"], deviceTaskConfig_process_types[eNOVA_A1]},
//     // {"Nova-A2", true, 115200, pms_id_list["A2_standard"], deviceTaskConfig_process_types[eNOVA_A2]},
//     // {"Nova-A3", true, 115200, pms_id_list["A2_standard"], deviceTaskConfig_process_types[eNOVA_A3]},
// };

const QMap<QString, ICommDevice::StCommStaticProperty> &CSprm::getDevPropertyList() {
    auto pms_id_list                    = getPmsIdList();
    auto deviceTaskConfig_process_types = getDeviceTaskConfigProcessTypes();

    static const QMap<QString, StCommStaticProperty> instance = {
        {"A1",
         StCommStaticProperty{
             StStaticProperty{StDeviceName{IDevice::EDeviceKind::eSENSOR_RANGE, "Nova", "A1"},
                              true,
                              &deviceTaskConfig_process_types.at((uint16_t)proximity_sensor::eNOVA_A2)},
             StCommInfo{StDevPortConfig{{IPort::eUART, {"", 19200}}}, CProtocolFactory::eMODEL_SIMPLE_CHECKSUM_P, pms_id_list["A1_standard"]}}},
        {"A1B",
         {{{IDevice::EDeviceKind::eSENSOR_RANGE, "Nova", "A1B"}, true, &deviceTaskConfig_process_types.at((uint16_t)proximity_sensor::eNOVA_A1)},
          {{{IPort::eUART, {"", 19200}}}, CProtocolFactory::eCLIENT_BESTER_P, pms_id_list["client_bester"]}}},
        {"A1C",
         {{{IDevice::EDeviceKind::eSENSOR_RANGE, "Nova", "A1C"}, true, &deviceTaskConfig_process_types.at((uint16_t)proximity_sensor::eNOVA_A1)},
          {{{IPort::eUART, {"", 19200}}}, CProtocolFactory::eCLIENT_BESTER_P, pms_id_list["client_bester"]}}},
        {"A1D",
         {{{IDevice::EDeviceKind::eSENSOR_RANGE, "Nova", "A1D"}, true, &deviceTaskConfig_process_types.at((uint16_t)proximity_sensor::eNOVA_A1)},
          {{{IPort::eUART, {"", 19200}}}, CProtocolFactory::eCLIENT_HELI_P, pms_id_list["client_HELI"]}}},
        {"A1K",
         {{{IDevice::EDeviceKind::eSENSOR_RANGE, "Nova", "A1K"}, true, &deviceTaskConfig_process_types.at((uint16_t)proximity_sensor::eNOVA_A1)},
          {{{IPort::eUART, {"", 19200}}}, CProtocolFactory::eMODEL_SIMPLE_CHECKSUM_P, pms_id_list["A1_standard"]}}},
        {"A1M",
         {{{IDevice::EDeviceKind::eSENSOR_RANGE, "Nova", "A1M"}, true, &deviceTaskConfig_process_types.at((uint16_t)proximity_sensor::eNOVA_A1)},
          {{{IPort::eUART, {"", 115200}}}, CProtocolFactory::eCLIENT_YOUSHENG_P, pms_id_list["client_YS"]}}},

        {"A2",
         {{{IDevice::EDeviceKind::eSENSOR_RANGE, "Nova", "A2"}, true, &deviceTaskConfig_process_types.at((uint16_t)proximity_sensor::eNOVA_A2)},
          {{{IPort::eUART, {"", 115200}}}, CProtocolFactory::eMODEL_SIMPLE_XOR_P, pms_id_list["A2_standard"]}}},
        {"A3",
         {{{IDevice::EDeviceKind::eSENSOR_RANGE, "Nova", "A3"}, true, &deviceTaskConfig_process_types.at((uint16_t)proximity_sensor::eNOVA_A3)},
          {{{IPort::eUART, {"", 115200}}}, CProtocolFactory::eMODEL_SIMPLE_XOR_P, pms_id_list["A2_standard"]}}},

    };
    return instance;
}
#if 1  //函数集组合
// const QList<deviceProc::StDevTaskInfo> CSprm::s_tasks_name = QList<deviceProc::StDevTaskInfo>()
//        << s_basic_tasks_name
//        << sm_universal_tasks_name;

CSprm::process_type CSprm::sc_processList;

// const QMap<QString, CSprm::NProcessTemplateC::StTaskConfig> CSprm::sm_basic_tasks = {
//    {CSprm::s_basic_tasks_name.at(ICommDevice::ECommDecBasicTasks::eTASK_VERSION).task_name, sc_processList.taskRegister(&CSprm::version, true, 0, 0, 50, 5)},
//    {CSprm::s_basic_tasks_name.at(ICommDevice::ECommDecBasicTasks::eTASK_CHIP_ID).task_name, sc_processList.taskRegister(&CSprm::chipId, true, 0, 0, 50, 5)},
//    {CSprm::s_basic_tasks_name.at(ICommDevice::eREAD_DDS).task_name, sc_processList.taskRegister(&CSprm::readDds, true, 0, 0, 50, 5)},
//};

// const QMap<QString, CSprm::NProcessTemplateC::StTaskConfig> CSprm::sm_calib_verify_tasks = {
//    {IProximitySensor::sm_universal_tasks_name[IProximitySensor::eCALIB_MODE].task_name, sc_processList.taskRegister(&CSprm::measureDist, true, 0, 0, 100,
//    5)}, {IProximitySensor::sm_universal_tasks_name[IProximitySensor::eCALIB_INFO_TASK].task_name, sc_processList.taskRegister(&CSprm::calibMode, true,
//    250, 0, 50, 5)}, {IProximitySensor::sm_universal_tasks_name[IProximitySensor::eTASK_VERIFY_MODE].task_name,
//    sc_processList.taskRegister(&CSprm::workMode, true, 250, 0, 50, 5)},
//    {IProximitySensor::sm_universal_tasks_name[IProximitySensor::eTASK_VERIFY].task_name, sc_processList.taskRegister(&CSprm::workMode, true, 250, 0,
//    50, 5)}, {IProximitySensor::sm_universal_tasks_name[IProximitySensor::eTASK_WORK_MODE].task_name, sc_processList.taskRegister(&CSprm::workMode, true,
//    250, 0, 50, 5)}, {IProximitySensor::sm_universal_tasks_name[IProximitySensor::eTASK_WORK].task_name, sc_processList.taskRegister(&CSprm::workMode,
//    true, 250, 0, 50, 5)},
//};

// 基础任务
const QMap<IProximitySensor::ECommDecBasicTasks, CSprm::process_type::StTaskInfo> CSprm::sm_basic_tasks = {
    {ICommDevice::ECommDecBasicTasks::eTASK_VERSION, sc_processList.taskRegister(&CSprm::version, true, false, 0, 0, 50, 5)},
    {ICommDevice::ECommDecBasicTasks::eTASK_CHIP_ID, sc_processList.taskRegister(&CSprm::chipId, true, false, 0, 0, 50, 5)},
    {ICommDevice::ECommDecBasicTasks::eTASK_READ_DDS, sc_processList.taskRegister(&CSprm::readDds, true, false, 2000, 0, 50, 5)},
};

// const QMap<IProximitySensor::EUniversalTasks, CSprm::NProcessTemplateC::StTaskConfig> CSprm::sm_calib_verify_tasks = {
//    {IProximitySensor::eCALIB_MODE, sc_processList.taskRegister(&CSprm::calibMode, true, 0, 0, 100, 5, false)},
//    {IProximitySensor::eCALIB_INFO_TASK, sc_processList.taskRegister(&CSprm::calibInfo, true, 0, 0, 0, 5, false)},
//    {IProximitySensor::eANALYSIS_MODE, sc_processList.taskRegister(&CSprm::analysisMode, true, 0, 0, 50, 5, false)},
//    {IProximitySensor::eANALYSIS_DATA_TASK, sc_processList.taskRegister(&CSprm::analysisData, true, 0, 0, 0, 5, false)},
//    {IProximitySensor::eTASK_VERIFY_MODE, sc_processList.taskRegister(&CSprm::verifyMode, true, 250, 0, 50, 5, false)},
//    {IProximitySensor::eTASK_VERIFY, sc_processList.taskRegister(&CSprm::measureDist, true, 250, 0, 50, 5, false)},
//    {IProximitySensor::eTASK_WORK_MODE, sc_processList.taskRegister(&CSprm::workMode, true, 250, 0, 50, 5, false)},
//    {IProximitySensor::eTASK_WORK, sc_processList.taskRegister(&CSprm::workInfo, true, 250, 0, 50, 5, false)},
//};
#else

#endif

// const QMap<QString, QMap<uint16_t, QString>> pms_id_list = {
//     {"A1_standard",
//      {
//          {0x01, "eCALIB1"},
//          {0x02, "eCALIB2"},
//          {0x04, "eSET_RANGE"},
//          {0x05, "eSELF_ADAPT"},
//          {0x06, "eLED_IO"},
//          {0x07, "eMODE_MODIFY"},
//          {0x08, "eDATA_MODE"},
//          {0x0A, "eQUERY_DIST"},
//          {0x0B, "eVERSION"},
//          {0x0C, "eRARAM_READ"},
//          {0x0D, "eCHIP_ID"},
//          {0x0E, "eDDS_LOG"},
//      }},
//     {"client_bester",
//      {
//          {0x00, "eQUERY_DIST"},
//          {0x01, "eSET_RANGE"},
//          {0x02, "eCALIB1"},
//          {0x03, "eCALIB2"},
//          {0x07, "eMODE_MODIFY"},
//          {0x08, "eDATA_MODE"},
//          {0x0A, "eEXPAND_DIST"},
//          {0x0B, "eVERSION"},
//          {0x0C, "eRARAM_READ"},
//          {0x0D, "eCHIP_ID"},
//          {0x0E, "eDDS_LOG"},
//      }},
//     {"client_HELI",
//      {
//          {0x01, "eCALIB1"},
//          {0x02, "eCALIB2"},
//          {0x04, "eSET_RANGE"},
//          {0x05, "eSELF_ADAPT"},
//          {0x06, "eLED_IO"},
//          {0x07, "eMODE_MODIFY"},
//          {0x08, "eDATA_MODE"},
//          {0x09, "eLOG_IO"},
//          {0x0A, "eQUERY_DIST"},
//          {0x0B, "eVERSION"},
//          {0x0C, "eRARAM_READ"},
//          {0x0D, "eCHIP_ID"},
//          {0x0E, "eDDS_LOG"},
//          {0xFA, "eCUSTOM_VERSION"},
//      }},
//     {"client_YS",
//      {
//          {0x01, "eCALIB1"},
//          {0x02, "eCALIB2"},
//          {0x06, "eLED_IO"},
//          {0x07, "eMODE_MODIFY"},
//          {0x08, "eDATA_MODE"},
//          {0x0A, "eQUERY_DIST"},
//          {0x0B, "eVERSION"},
//          {0x0C, "eRARAM_READ"},
//          {0x0D, "eCHIP_ID"},
//          {0x0E, "eDDS_LOG"},
//      }},
//     {"A2_standard",
//      {
//          {0x01, "eCHIP_ID"},
//          {0x02, "eVERSION"},
//          {0x05, "ePROC_MODE"},
//          {0x06, "eLED_IO"},
//          {0x07, "eCALIB_ITEM"},
//          {0x08, "eCOM_BUAD"},
//          {0x09, "ePARAM"},
//          {0x0A, "eQUERY_DIST"},
//          {0x0B, "eINTEGRAL_CNT"},
//          {0x0C, "eSAMPLE_VOL_VARS"},
//          {0x0E, "eDDS"},
//      }},
// };

/**
 * @brief 注册设备
 *
 * @return true
 * @return false
 */
bool CSprm::registerCommDeviceType(void) {
    // 注册 Nova 系列设备
    const auto &property_list = getDevPropertyList();
    for (auto it = property_list.begin(); it != property_list.end(); it++) {
        device::factory::IDeviceFactory::getInstance().registerCommDeviceType(
            it->static_property.device_name.full_name,
            [](IPort *port, const QString &model, const QString &serial_num) -> IProximitySensor * { return new CSprm(port, model, serial_num); },
            [it]() -> IComm::StDevPortConfig { return it->comm_info.default_port_config; });
    }
    return true;
}

CSprm::CSprm(IPort *port_, const QString &model_name, const QString &serial_num)
    : IProximitySensor(port_), mc_calib_process_(new TCalib_process), mst_calib_task_status_(new TCalib_process::StStepRecord), m_is_ack(false) {

    qRegisterMetaType<EPmsAllId>("sprm::EPmsAllId");
    qRegisterMetaType<ESysMode>("sprm::ESysMode");
    qRegisterMetaType<ELedMode>("sprm::ELedMode");

    m_id_Enum = QMetaEnum::fromType<EPmsAllId>();


    // 1.1 init property
    const auto &property_list = getDevPropertyList();

    auto it = property_list.find(model_name);
    if (it != property_list.end()) {
        mst_property.static_property_ = &it->static_property;
        mst_comm_info_                = &it->comm_info;
    } else {
        mst_property.static_property_ = &property_list.begin()->static_property;
        mst_comm_info_                = &property_list.begin()->comm_info;

        LOG_ERROR(MyLogger::LogType::INIT, QString("don't find device %1").arg(model_name));
    }
    if (port_)
        this->setInstanceName(port_->getPortName(), serial_num);


    // 1.2 cmd
    mst_cmd_config.cmd_file_name = "nova_proj_cmd";
    mst_cmd_config.root_name     = "project_cmd";

    // method-1: comm
    mst_protocol_comm.analysis_protocol_    = new CModel_pmsCurveP;
    mst_protocol_comm.interaction_protocol_ = CProtocolFactory::getInstance().protocolCreate(mst_comm_info_->protocol);

    LOG_INFO(MyLogger::LogType::INIT, QString("Protocol class log:"));
    LOG_INFO(MyLogger::LogType::INIT, QString("Protocol class: %1").arg(typeid(*mst_protocol_comm.interaction_protocol_).name()));

    mst_protocol_comm.data_protocol_ = CProtocolFactory::getInstance().protocolCreate(mst_comm_info_->protocol);

    projCommUpdate();

    // method-2: calib info
    qRegisterMetaType<CSprm::ECommStep>("CSprm::ECommStep");    //注册自定义类型
    qRegisterMetaType<CSprm::ECalibStep>("CSprm::ECalibStep");  //注册自定义类型
    qRegisterMetaType<QMap<QString, QString>>("QMap<QString, QString>");

    loadConfigParamInit();

    calibInit();

    // method-3: production process
    //* default calib config
    mm_nova_model_config["codeScanner"] = "ON";  //
    mm_nova_model_config["clean_log"]   = "ON";
    mm_nova_model_config["version"]     = "ON";  //
    mm_nova_model_config["chipID"]      = "ON";
    mm_nova_model_config["calib"]       = "ON";

    mm_nova_model_config["deepCalib"] = "OFF";
    mm_nova_model_config["accuracy"]  = "ON";

    mm_nova_model_config["current"]  = "OFF";
    mm_nova_model_config["voltage"]  = "OFF";
    mm_nova_model_config["led_test"] = "OFF";
    mm_nova_model_config["trigger"]  = "OFF";

    mm_nova_model_config["shipping_status_reset"] = "OFF";
    mm_nova_model_config["dds"]                   = "OFF";
    //    mm_nova_model_config["auto_mark"]             = "OFF"; //打标签

    mm_nova_model_config["calib1_angle"] = "0";
    mm_nova_model_config["calib2_angle"] = "90";

    mm_nova_model_config["current_monitor_dev"] = "1";  // 1-集成功能测试模块；2-独立功耗模块
    mm_nova_model_config["voltage_monitor_dev"] = "1";  // 1-集成功能测试模块；2-独立功耗模块

    // method4: main tasks
    //* default calib verify tasks
    //    m_calib_verify_tasks = {
    //        {IProximitySensor::eCALIB_MODE, sc_processList.taskRegister(&CSprm::calibMode, true, 0, 0, 100, 5, false)},
    //        {IProximitySensor::eCALIB_INFO_TASK, sc_processList.taskRegister(&CSprm::calibInfo, true, 0, 0, 0, 5, false)},
    //        {IProximitySensor::eANALYSIS_MODE, sc_processList.taskRegister(&CSprm::analysisMode, true, 0, 0, 50, 5, false)},
    //        {IProximitySensor::eANALYSIS_DATA_TASK, sc_processList.taskRegister(&CSprm::analysisData, true, 0, 0, 0, 5, false)},
    //        {IProximitySensor::eTASK_VERIFY_MODE, sc_processList.taskRegister(&CSprm::verifyMode, true, 250, 0, 50, 5, false)},
    //        {IProximitySensor::eTASK_VERIFY, sc_processList.taskRegister(&CSprm::measureDist, true, 250, 0, 50, 5, false)},
    //        {IProximitySensor::eTASK_WORK_MODE, sc_processList.taskRegister(&CSprm::workMode, true, 250, 0, 50, 5, false)},
    //        {IProximitySensor::eTASK_WORK, sc_processList.taskRegister(&CSprm::workInfo, true, 250, 0, 50, 5, false)},
    //    };

    //********************************** varibles init **********************************
    moduleUsedDataClean();
    cleanOptimizeData();
    mu_dds.all_dds = 0;
}

CSprm::~CSprm() {
    delete mc_calib_process_;
    delete mst_calib_task_status_;
}

bool CSprm::registerName() {
    QStringList tasks_list;
    for (auto it = IProximitySensor::sm_basic_tasks_name.begin(); it != IProximitySensor::sm_basic_tasks_name.end(); it++)
        tasks_list.append(it->task_name);
    for (auto it = IProximitySensor::sm_universal_tasks_name.begin(); it != IProximitySensor::sm_universal_tasks_name.end(); it++)
        tasks_list.append(it->task_name);

    IDeviceInstance::getInstance().registerObject("Nova-A1", tasks_list);
    IDeviceInstance::getInstance().registerObject("Nova-A1H", tasks_list);
    return true;
}


void CSprm::calibInit(void) {
    //* 区分不同芯片的校正流程与标准
    StCalibFunc calib_func_tmp;
    calib_func_tmp.ptr_     = (typeFptr_)&CSprm::calibTask1;
    calib_func_tmp.ack_ptr_ = (typeFptr_)&CSprm::calibTask1Ack;
    mv_calib_func.append(calib_func_tmp);

    calib_func_tmp.ptr_     = (typeFptr_)&CSprm::calibTask2;
    calib_func_tmp.ack_ptr_ = (typeFptr_)&CSprm::calibTask2Ack;
    mv_calib_func.append(calib_func_tmp);

    calib_func_tmp.ptr_     = (typeFptr_)&CSprm::calibTask3;
    calib_func_tmp.ack_ptr_ = (typeFptr_)&CSprm::calibTask3Ack;
    mv_calib_func.append(calib_func_tmp);

    calib_func_tmp.ptr_     = (typeFptr_)&CSprm::calibTask4;
    calib_func_tmp.ack_ptr_ = (typeFptr_)&CSprm::calibTask4Ack;
    mv_calib_func.append(calib_func_tmp);


    QMapIterator<ISprmSoc::ECalibProcess, ISprmSoc::StCalibItemConfig> m_iter(mi_spms_soc_->mm_calib_config);
    while (m_iter.hasNext()) {
        m_iter.next();

        //******************************* 存储需要的校正步骤（可以calib1,calib3跳着校正，使用于校正步骤组合），执行时轮询存储的步骤 *********************
        //****************************** 不同SOC校正步骤不固定，先不使用该方式（不用组合的方式） *************
        //* 枚举转字符
        //        if(mv_calib_items_->size() > 0) {
        uint8_t calib_index = (uint8_t)m_iter.key();
        mv_calib_task_list.append(mc_calib_process_->addCallbackFunction((pmsFptr_)mv_calib_func.at(calib_index).ptr_,
                                                                         (pmsFptr_)mv_calib_func.at(calib_index).ack_ptr_,
                                                                         m_iter.value().calib_flag,
                                                                         0,
                                                                         0,
                                                                         800,
                                                                         5));  //校正需要较长时间
    }
    mc_calib_process_->taskInit(&mv_calib_task_list, mst_calib_task_status_);
}
/**
 * @brief CSprm::loadConfigParamInit
 */
void CSprm::loadConfigParamInit() {
    //* pms 配置地址
    //    QString config_file_name = QApplication::applicationDirPath() + "/config/device/pms/" + mst_cmd_config.proj_name + "_config.ini";
    //    qDebug() << "pms/ device config path: " << config_file_name;

    //    //* 获取配置: model默认信息 + 参数配置
    //    //    mst_iniConfig = CSprmReadIni::getInstance(config_file_name)->getIniConfig(); //, mi_spms_soc_->mm_calib_config
    //    CSprmReadIni::getInstance()->getIniConfig(config_file_name, mst_iniConfig);  //, mi_spms_soc_->mm_calib_config

    //    mi_spms_soc_ = CSprmSocFactory::getInstance().spmsSocCreate(mst_iniConfig.soc_model, mst_iniConfig.judge_method);
    //    CSprmReadIni::getInstance()->getParamsConfig(config_file_name, mi_spms_soc_->mm_calib_config);

    //*
    QString config_file_name = QApplication::applicationDirPath() + "/config/nova/" + mst_cmd_config.proj_name + "_config.xml";
    mi_xml_load_->readParam(config_file_name, &mm_nova_model_config);

    LOG_INFO(MyLogger::LogType::INIT, QString("device config path: ").arg(config_file_name));

    //* 1. model默认信息 + 参数配置
    if (CSprmReadIni::getInstance()->getIniConfig(config_file_name, mst_iniConfig, mst_property.config_property.mark_info)) {
        //        mst_device_property_->mark_info = mst_iniConfig.basic_info;
    } else
        mu_dds.dds.local_config_basic = 1;

    //* 2. 模组特性, 上电时间。。。
    if (!CSprmReadIni::getInstance()->getModuleFeature(config_file_name, mst_feature_config))
        mu_dds.dds.local_config_feature = 1;  //, mi_spms_soc_->mm_calib_config

    //* 3. 功能，
    if (!CSprmReadIni::getInstance()->getFunctionConfig(config_file_name, mst_function_config))
        mu_dds.dds.local_config_function = 1;  //, mi_spms_soc_->mm_calib_config

    //* 4. 校正配置
    mi_spms_soc_ = CSprmSocFactory::getInstance().spmsSocCreate(mst_iniConfig.soc_model, mst_iniConfig.judge_method);
    if (!CSprmReadIni::getInstance()->getCalibConfig(config_file_name, mi_spms_soc_->mm_calib_config))
        mu_dds.dds.local_config_calib = 1;

    //* 5. optimize config
    if (!CSprmReadIni::getInstance()->getFittingConfig(config_file_name, mv_interval_local_config))
        mu_device_accuracy_dds.dds.local_config_err = 1;
    else {
        localIntervalInit();
    }
    //* 6. process status update
    processStatusUpdate();

    //* 测试流程(功能模块中读取)
    //    config_file_name = QApplication::applicationDirPath() + "/config/nova/" + mst_iniConfig.basic_info.dev_name + "_config.xml";
    //    mi_load_->readParam(config_file_name, &mm_nova_model_config);
}

#if 1
void CSprm::cmdInit() {
    QMap<QString, QByteArray> init_cmd;
    QString                   step_str;

    //    QMetaEnum idEnum             = QMetaEnum::fromType<EPmsAllId>();
    //    QMetaEnum modeEnum           = QMetaEnum::fromType<CSprm::EMode>();
    //    QMetaEnum ledModeEnum        = QMetaEnum::fromType<CSprm::ELedMode>();
    //    QMetaEnum dataOutputModeEnum = QMetaEnum::fromType<CSprm::EDataOutputMode>();
    if (!mst_protocol_comm.interaction_protocol_) {
        LOG_ERROR(MyLogger::LogType::ERROR_LOG, QString(""));
        return;
    }

    for (auto it = mst_comm_info_->cmd_id.begin(); it != mst_comm_info_->cmd_id.end(); it++) {
        // control cmd
        if ((uint16_t)IBaseProtocol::ECmdType::eCTRL == (it->type & (uint16_t)IBaseProtocol::ECmdType::eCTRL)) {
            init_cmd.insert(CTypeConvert::enumToString(m_id_Enum, it.key()), mst_protocol_comm.interaction_protocol_->getControlCmd((uint8_t)it.value().mark));
        }
        if ((uint16_t)IBaseProtocol::ECmdType::eREAD == (it->type & (uint16_t)IBaseProtocol::ECmdType::eREAD)) {  //
            init_cmd.insert(CTypeConvert::enumToString(m_id_Enum, it.key()),
                            mst_protocol_comm.interaction_protocol_->getReadCmd((uint8_t)it.value().mark, NULL));
        }
        if ((uint16_t)IBaseProtocol::ECmdType::eWRITE == (it->type & (uint16_t)IBaseProtocol::ECmdType::eWRITE)) {  // write
            // add items cmd under id
            if (it.value().options.size() != 0) {
                for (auto sub_it = it.value().options.begin(); sub_it != it.value().options.end(); sub_it++) {
                    init_cmd.insert(
                        QString("%1_%2").arg(CTypeConvert::enumToString(m_id_Enum, it.key())).arg(sub_it.key()),
                        mst_protocol_comm.interaction_protocol_->getWriteCmd((uint8_t)it.value().mark, CTypeConvert::enumToByteArray(sub_it.value())));
                }
            } else {
                LOG_ERROR(MyLogger::LogType::ERROR_LOG, QString());
            }
        }
    }

    //* 1. load cmd from xml, if cmd is empty, init it
    for (QMap<QString, QByteArray>::iterator iter = init_cmd.begin(); iter != init_cmd.end(); iter++) {
        if (mst_protocol_comm.interaction_cmd.contains(iter.key())) {  // if xml contain that cmd
            if (mst_protocol_comm.interaction_cmd.value(iter.key()).size() == 0) {
                mst_protocol_comm.interaction_cmd.insert(iter.key(), iter.value());
            }
        } else {  // add
            mst_protocol_comm.interaction_cmd.insert(iter.key(), iter.value());
        }
    }

    //* 2. update cmd to xml
    saveCmdList(mst_protocol_comm.interaction_cmd);
}

#else
void CSprm::cmdInit() {
    QMap<QString, QByteArray> init_cmd;

    QMetaEnum stepEnum           = QMetaEnum::fromType<proximity_sensor::EPmsAllId>();
    QMetaEnum modeEnum           = QMetaEnum::fromType<CSprm::EMode>();
    QMetaEnum ledModeEnum        = QMetaEnum::fromType<CSprm::ELedMode>();
    QMetaEnum dataOutputModeEnum = QMetaEnum::fromType<CSprm::EDataOutputMode>();

    if (mst_property.name == "A1_standard") {
        //* 1. load cmd from xml, if cmd is empty, init it
        QString step_str;

        //* control cmd
        init_cmd.insert(stepEnum.valueToKey((uint8_t)proximity_sensor::eCALIB2), mst_protocol_comm.interaction_protocol_->getControlCmd(mst_comm_info.cmd_id[(uint8_t)proximity_sensor::eCALIB1]);
        init_cmd.insert(stepEnum.valueToKey((uint8_t)proximity_sensor::eCALIB2), mst_protocol_comm.interaction_protocol_->getControlCmd((uint8_t)proximity_sensor::eCALIB2));
        init_cmd.insert(stepEnum.valueToKey((uint8_t)proximity_sensor::eSELF_ADAPT), mst_protocol_comm.interaction_protocol_->getControlCmd((uint8_t)proximity_sensor::eSELF_ADAPT));

        //* write cmd
        init_cmd.insert(QString("%1_%2").arg(stepEnum.valueToKey((uint8_t)proximity_sensor::eMODE_MODIFY)).arg(modeEnum.valueToKey((uint8_t)EMode::kMEASURE_MODE)), mst_protocol_comm.interaction_protocol_->getWriteCmd((uint8_t)proximity_sensor::eMODE_MODIFY, CTypeConvert::enumToByteArray(static_cast<uint8_t>(kMEASURE_MODE))));
        init_cmd.insert(QString("%1_%2").arg(stepEnum.valueToKey((uint8_t)proximity_sensor::eMODE_MODIFY)).arg(modeEnum.valueToKey((uint8_t)EMode::kCALIB_MODE)), mst_protocol_comm.interaction_protocol_->getWriteCmd((uint8_t)proximity_sensor::eMODE_MODIFY, CTypeConvert::enumToByteArray(static_cast<uint8_t>(kCALIB_MODE))));
        init_cmd.insert(QString("%1_%2").arg(stepEnum.valueToKey((uint8_t)proximity_sensor::eMODE_MODIFY)).arg(modeEnum.valueToKey((uint8_t)EMode::kTRIGGER_MODE)), mst_protocol_comm.interaction_protocol_->getWriteCmd((uint8_t)proximity_sensor::eMODE_MODIFY, CTypeConvert::enumToByteArray(static_cast<uint8_t>(kTRIGGER_MODE))));
        init_cmd.insert(QString("%1_%2").arg(stepEnum.valueToKey((uint8_t)proximity_sensor::eMODE_MODIFY)).arg(modeEnum.valueToKey((uint8_t)EMode::kSTANDBY_MODE)), mst_protocol_comm.interaction_protocol_->getWriteCmd((uint8_t)proximity_sensor::eMODE_MODIFY, CTypeConvert::enumToByteArray(static_cast<uint8_t>(kSTANDBY_MODE))));

        init_cmd.insert(QString("%1_%2").arg(stepEnum.valueToKey((uint8_t)proximity_sensor::eLED_IO)).arg(ledModeEnum.valueToKey((uint8_t)ELedMode::eCLOSE)), mst_protocol_comm.interaction_protocol_->getWriteCmd((uint8_t)proximity_sensor::eLED_IO, CTypeConvert::enumToByteArray(static_cast<uint8_t>(eCLOSE))));
        init_cmd.insert(QString("%1_%2").arg(stepEnum.valueToKey((uint8_t)proximity_sensor::eLED_IO)).arg(ledModeEnum.valueToKey((uint8_t)ELedMode::eOPEN)), mst_protocol_comm.interaction_protocol_->getWriteCmd((uint8_t)proximity_sensor::eLED_IO, CTypeConvert::enumToByteArray(static_cast<uint8_t>(eOPEN))));
        init_cmd.insert(QString("%1_%2").arg(stepEnum.valueToKey((uint8_t)proximity_sensor::eLED_IO)).arg(ledModeEnum.valueToKey((uint8_t)ELedMode::eBREATH)), mst_protocol_comm.interaction_protocol_->getWriteCmd((uint8_t)proximity_sensor::eLED_IO, CTypeConvert::enumToByteArray(static_cast<uint8_t>(eBREATH))));
        init_cmd.insert(QString("%1_%2").arg(stepEnum.valueToKey((uint8_t)proximity_sensor::eLED_IO)).arg(ledModeEnum.valueToKey((uint8_t)ELedMode::eFLICKER)), mst_protocol_comm.interaction_protocol_->getWriteCmd((uint8_t)proximity_sensor::eLED_IO, CTypeConvert::enumToByteArray(static_cast<uint8_t>(eFLICKER))));

        init_cmd.insert(QString("%1_%2").arg(stepEnum.valueToKey((uint8_t)proximity_sensor::eDATA_MODE)).arg(dataOutputModeEnum.valueToKey((uint8_t)EDataOutputMode::eNOT_OUTPUT_MODE)),
                        mst_protocol_comm.interaction_protocol_->getWriteCmd((uint8_t)proximity_sensor::eDATA_MODE, CTypeConvert::enumToByteArray(static_cast<uint8_t>(eNOT_OUTPUT_MODE))));
        init_cmd.insert(QString("%1_%2").arg(stepEnum.valueToKey((uint8_t)proximity_sensor::eDATA_MODE)).arg(dataOutputModeEnum.valueToKey((uint8_t)EDataOutputMode::eCURVE_SHOW_OUTPUT_MODE)),
                        mst_protocol_comm.interaction_protocol_->getWriteCmd((uint8_t)proximity_sensor::eDATA_MODE, CTypeConvert::enumToByteArray(static_cast<uint8_t>(eCURVE_SHOW_OUTPUT_MODE))));
        init_cmd.insert(QString("%1_%2").arg(stepEnum.valueToKey((uint8_t)proximity_sensor::eDATA_MODE)).arg(dataOutputModeEnum.valueToKey((uint8_t)EDataOutputMode::eDISTANCE_OUTPUT_MODE)),
                        mst_protocol_comm.interaction_protocol_->getWriteCmd((uint8_t)proximity_sensor::eDATA_MODE, CTypeConvert::enumToByteArray(static_cast<uint8_t>(eDISTANCE_OUTPUT_MODE))));

        //    init_cmd.insert(stepEnum.valueToKey((uint8_t)proximity_sensor::eDATA_MODE), mst_protocol_comm.interaction_protocol_->getWriteCmd((uint8_t)proximity_sensor::eDATA_MODE,
        //    CTypeConvert::enumToByteArray(static_cast<uint8_t>(eSTART_OUTPUT))));
        init_cmd.insert(stepEnum.valueToKey((uint8_t)proximity_sensor::eQUERY_DIST), mst_protocol_comm.interaction_protocol_->getWriteCmd((uint8_t)proximity_sensor::eQUERY_DIST, CTypeConvert::enumToByteArray(static_cast<uint8_t>(0x00))));

        //* read cmd
        QByteArray r_data;
        for (uint8_t for_i = (uint8_t)proximity_sensor::eVERSION; for_i <= (uint8_t)proximity_sensor::eDDS_LOG; for_i++) {
            //        init_cmd.insert(stepEnum.valueToKey(for_i), mst_protocol_comm.interaction_protocol_->getReadCmd(for_i, r_data));
            init_cmd.insert(stepEnum.valueToKey(for_i), mst_protocol_comm.interaction_protocol_->getControlCmd(for_i));
        }
        init_cmd.insert(stepEnum.valueToKey((uint8_t)proximity_sensor::eCUSTOM_VERSION), mst_protocol_comm.interaction_protocol_->getControlCmd((uint8_t)proximity_sensor::eCUSTOM_VERSION));

        for (QMap<QString, QByteArray>::iterator iter = init_cmd.begin(); iter != init_cmd.end(); iter++) {
            if (mst_protocol_comm.interaction_cmd.contains(iter.key())) {  // if xml contain that cmd
                if (mst_protocol_comm.interaction_cmd.value(iter.key()).size() == 0) {
                    mst_protocol_comm.interaction_cmd.insert(iter.key(), iter.value());
                }
            } else {  // add
                mst_protocol_comm.interaction_cmd.insert(iter.key(), iter.value());
            }
        }

        //* 2. update cmd to xml
        QString filename = QApplication::applicationDirPath() + "/cmdList/nova_proj_cmd.xml";
        mi_load_->writeParam(filename, "project_cmd", "nova-A1D", &mst_protocol_comm.interaction_cmd);

    } else if (mst_property.name == "client_bester") {
        QMetaEnum                 stepEnum = QMetaEnum::fromType<CPmsA1B::proximity_sensor>();
        QString                   step_str;
        QMap<QString, QByteArray> init_cmd;

        //* control cmd
        //    for(uint8_t for_i = (uint8_t)eCALIB1; for_i < uint8_t(eLOG_OUTPUT); for_i++) {
        //        step_str = stepEnum.valueToKey(for_i);

        //        init_cmd.insert(step_str, m_protocol_->getWriteCmd(for_i, ));
        //    }
        init_cmd.insert(stepEnum.valueToKey((uint8_t)proximity_sensor::eQUERY_DIST),
                        mst_protocol_comm.interaction_protocol_->getControlCmd((uint8_t)proximity_sensor::eQUERY_DIST));
        init_cmd.insert(stepEnum.valueToKey((uint8_t)proximity_sensor::eSET_RANGE),
                        mst_protocol_comm.interaction_protocol_->getControlCmd((uint8_t)proximity_sensor::eSET_RANGE));
        init_cmd.insert(stepEnum.valueToKey((uint8_t)proximity_sensor::eCALIB1),
                        mst_protocol_comm.interaction_protocol_->getControlCmd((uint8_t)proximity_sensor::eCALIB1));
        init_cmd.insert(stepEnum.valueToKey((uint8_t)proximity_sensor::eCALIB2),
                        mst_protocol_comm.interaction_protocol_->getControlCmd((uint8_t)proximity_sensor::eCALIB2));

        //* write cmd
        //    init_cmd.insert(stepEnum.valueToKey((uint8_t)proximity_sensor::eMODE_MODIFY),
        //    mst_protocol_comm.interaction_protocol_->getWriteCmd((uint8_t)proximity_sensor::eMODE_MODIFY,
        //    CTypeConvert::enumToByteArray(static_cast<uint8_t>(eInteraction_mode))));
        init_cmd.insert(stepEnum.valueToKey((uint8_t)proximity_sensor::eDATA_MODE),
                        mst_protocol_comm.interaction_protocol_->getWriteCmd((uint8_t)proximity_sensor::eDATA_MODE,
                                                                             CTypeConvert::enumToByteArray(static_cast<uint8_t>(eSTART_OUTPUT))));

        //* read cmd
        QByteArray r_data;
        for (uint8_t for_i = (uint8_t)proximity_sensor::eEXPAND_DIST; for_i <= (uint8_t)proximity_sensor::eDDS_LOG; for_i++) {
            //        init_cmd.insert(stepEnum.valueToKey(for_i), mst_protocol_comm.interaction_protocol_->getReadCmd(for_i, r_data));
            init_cmd.insert(stepEnum.valueToKey(for_i), mst_protocol_comm.interaction_protocol_->getControlCmd(for_i));
        }

        for (QMap<QString, QByteArray>::iterator iter = init_cmd.begin(); iter != init_cmd.end(); iter++) {
            if (mst_protocol_comm.interaction_cmd.contains(iter.key())) {  // if xml contain that cmd
                if (mst_protocol_comm.interaction_cmd.value(iter.key()).size() == 0) {
                    mst_protocol_comm.interaction_cmd.insert(iter.key(), iter.value());
                }
            } else {  // add
                mst_protocol_comm.interaction_cmd.insert(iter.key(), iter.value());
            }
        }

        //* 2. update cmd to xml
        QString filename = QApplication::applicationDirPath() + "/cmdList/nova_proj_cmd.xml";
        mi_load_->writeParam(filename, "project_cmd", "nova-A1B", &mst_protocol_comm.interaction_cmd);

    } else if (mst_property.name == "A2_standard") {
        QString                   step_str;
        QMap<QString, QByteArray> init_cmd;

        init_cmd.insert(m_id_Enum.valueToKey((uint8_t)proximity_sensor::eQUERY_DIST),
                        mst_protocol_comm.interaction_protocol_->getControlCmd((uint8_t)proximity_sensor::eQUERY_DIST));
        init_cmd.insert(m_id_Enum.valueToKey((uint8_t)proximity_sensor::eCHIP_ID),
                        mst_protocol_comm.interaction_protocol_->getControlCmd((uint8_t)proximity_sensor::eCHIP_ID));
        init_cmd.insert(m_id_Enum.valueToKey((uint8_t)proximity_sensor::eVERSION),
                        mst_protocol_comm.interaction_protocol_->getControlCmd((uint8_t)proximity_sensor::eVERSION));
        init_cmd.insert(m_id_Enum.valueToKey((uint8_t)proximity_sensor::eDDS),
                        mst_protocol_comm.interaction_protocol_->getControlCmd((uint8_t)proximity_sensor::eDDS));

        //#define LIDAR_CMD(type, mode, n) \\\
       //    type _s = mode;\\\
       //    init_cmd.insert(QString("%1_%2"). \\\
       //    arg(m_id_Enum.valueToKey((uint8_t)proximity_sensor::ePROC_MODE)). \\\
       //    arg(lidar_mode.valueToKey(_s), \\\
       //    mst_protocol_comm.interaction_protocol_->getWriteCmd((uint8_t)proximity_sensor::ePROC_MODE, CTypeConvert::arrayToByteArray((uint8_t*)&(_s), n))));


        //* write cmd
        //** histogram mode
        modeCmdInit(init_cmd, ELidarMode::eTOF_TEST_MODE);
        modeCmdInit(init_cmd, ELidarMode::eTOF_HISTOGRAM_MODE);
        modeCmdInit(init_cmd, ELidarMode::eTOF_CALIBRATION_MODE);
        modeCmdInit(init_cmd, ELidarMode::eTOF_OPTIMIZATION_MODE);
        modeCmdInit(init_cmd, ELidarMode::eTOF_SINGLE_RANGE_MODE);
        modeCmdInit(init_cmd, ELidarMode::eTOF_CONTINUE_RANGE_MODE);
        modeCmdInit(init_cmd, ELidarMode::eTOF_WORK_MODE);

        calibCmdInit(init_cmd, ECalibMode::eXTALK);
        calibCmdInit(init_cmd, ECalibMode::eOFFSET);

        //* LED
        uint8_t io = 1;
        init_cmd.insert(m_id_Enum.valueToKey((uint8_t)proximity_sensor::eLED_IO),
                        mst_protocol_comm.interaction_protocol_->getWriteCmd(
                            (uint8_t)proximity_sensor::eLED_IO, CTypeConvert::arrayToByteArray((uint8_t *)&(io), 1)));  // reinterpret_cast<uint8_t*>(2), 1)));

        //** switch fre
        uint8_t fre = 1;
        init_cmd.insert(m_id_Enum.valueToKey((uint8_t)proximity_sensor::ePARAM),
                        mst_protocol_comm.interaction_protocol_->getWriteCmd(
                            (uint8_t)proximity_sensor::ePARAM, CTypeConvert::arrayToByteArray((uint8_t *)&(fre), 2)));  // reinterpret_cast<uint8_t*>(2), 1)));

        //** set integral counts
        uint32_t int_cnt = 0x20000;
        init_cmd.insert(m_id_Enum.valueToKey((uint8_t)proximity_sensor::eINTEGRAL_CNT),
                        mst_protocol_comm.interaction_protocol_->getWriteCmd(
                            (uint8_t)proximity_sensor::eINTEGRAL_CNT,
                            CTypeConvert::arrayToByteArray((uint8_t *)&(int_cnt), 4)));  // reinterpret_cast<uint8_t*>(3000), 4)));

        //** read and write vol vars
        uint32_t vol_factor = 100;
        init_cmd.insert(QString("%1_%2").arg(m_id_Enum.valueToKey((uint8_t)proximity_sensor::eSAMPLE_VOL_VARS)).arg("r"),
                        mst_protocol_comm.interaction_protocol_->getReadCmd((uint8_t)proximity_sensor::eSAMPLE_VOL_VARS,
                                                                            CTypeConvert::arrayToByteArray((uint8_t *)&(vol_factor), 2)));
        init_cmd.insert(QString("%1_%2").arg(m_id_Enum.valueToKey((uint8_t)proximity_sensor::eSAMPLE_VOL_VARS)).arg("w"),
                        mst_protocol_comm.interaction_protocol_->getWriteCmd(
                            (uint8_t)proximity_sensor::eSAMPLE_VOL_VARS,
                            CTypeConvert::arrayToByteArray((uint8_t *)&(vol_factor), 2)));  // reinterpret_cast<uint8_t*>(3000), 4)));
        //* sysc
        for (QMap<QString, QByteArray>::iterator iter = init_cmd.begin(); iter != init_cmd.end(); iter++) {
            if (mst_protocol_comm.interaction_cmd.contains(iter.key())) {  // if xml contain that cmd
                if (mst_protocol_comm.interaction_cmd.value(iter.key()).size() == 0) {
                    mst_protocol_comm.interaction_cmd.insert(iter.key(), iter.value());
                }
            } else {  // add
                mst_protocol_comm.interaction_cmd.insert(iter.key(), iter.value());
            }
        }

        //* 2. update cmd to xml
        saveCmdList(mst_protocol_comm.interaction_cmd);
    } else {
    }
}
#endif

// CPortFactory::StCreateConfig CSprm::getPortConfig() {
//    return mst_comm_info_->default_port_config.port_config;
//}

void CSprm::taskStatusUpdate(UProcessAllTasks *st_process_status_) {
    if (mm_nova_model_config["codeScanner"] == proximity_sensor::process_on)
        st_process_status_->all_task.codeScanner = true;  //
    if (mm_nova_model_config["clean_log"] == proximity_sensor::process_on)
        st_process_status_->all_task.clean_dds = true;
    if (mm_nova_model_config["version"] == proximity_sensor::process_on)
        st_process_status_->all_task.version = true;  //
    if (mm_nova_model_config["chipID"] == proximity_sensor::process_on)
        st_process_status_->all_task.chipID = true;
    if (mm_nova_model_config["calib"] == proximity_sensor::process_on)
        st_process_status_->all_task.calib = true;  // = "ON";
    if (mm_nova_model_config["deepCalib"] == proximity_sensor::process_on)
        st_process_status_->all_task.deepCalib = true;

    if (mm_nova_model_config["accuracy"] == proximity_sensor::process_on)
        st_process_status_->all_task.accuracy = true;
    //    if(mm_nova_model_config["accuracy2"] == proximity_sensor::process_on) st_process_status_->all_task.accuracy2 = true;
    //    if(mm_nova_model_config["accuracy3"] == proximity_sensor::process_on) st_process_status_->all_task.accuracy3 = true;
    //    if(mm_nova_model_config["accuracy4"] == proximity_sensor::process_on) st_process_status_->all_task.accuracy4 = true;
    //    if(mm_nova_model_config["accuracy5"] == proximity_sensor::process_on) st_process_status_->all_task.accuracy5 = true;
    //    if(mm_nova_model_config["accuracy6"] == proximity_sensor::process_on) st_process_status_->all_task.accuracy6 = true;

    if (mm_nova_model_config["current"] == proximity_sensor::process_on)
        st_process_status_->all_task.current = true;
    if (mm_nova_model_config["voltage"] == proximity_sensor::process_on)
        st_process_status_->all_task.voltage = true;
    if (mm_nova_model_config["led_test"] == proximity_sensor::process_on)
        st_process_status_->all_task.led = true;
    if (mm_nova_model_config["trigger"] == proximity_sensor::process_on)
        st_process_status_->all_task.trigger = true;

    if (mm_nova_model_config["shipping_status_reset"] == proximity_sensor::process_on)
        st_process_status_->all_task.reset_status = true;
    if (mm_nova_model_config["dds"] == proximity_sensor::process_on)
        st_process_status_->all_task.moduleDds = true;
    if (mm_nova_model_config["auto_mark"] == proximity_sensor::process_on)
        st_process_status_->all_task.auto_mark = true;
}

void CSprm::calibConfigUpdate(QVector<CSprm::StCalibItems> *v_calib_items_) {
    StCalibItems calib_items_tmp;
    uint8_t      index = 0;
    for (auto it = mi_spms_soc_->mm_calib_config.begin(); it != mi_spms_soc_->mm_calib_config.end(); it++) {
        index++;
        calib_items_tmp.angle = mm_nova_model_config["calib" + QString::number(index, 10) + "_angle"].toInt();
        v_calib_items_->append(calib_items_tmp);
    }
}

void CSprm::triggerStatusUpdate(StFunctionType &st_function_type) {
    st_function_type.current_type = (ECurrentModule)mm_nova_model_config["current_monitor_dev"].toUInt();
}

void CSprm::processStatusUpdate(StPmsProcessConfig &st_process_config) {
    QString config_file_name = QApplication::applicationDirPath() + "/config/device/pms/" + mst_cmd_config.proj_name + "_config.ini";

    CSprmReadIni::getInstance()->getProcessConfig(config_file_name, st_process_config);
}

void CSprm::moduleUsedDataClean() {
    mst_used_data.distance.cleanData();
    mst_used_data.confidence.cleanData();
    mst_used_data.trigger_status = true;
}

void CSprm::databaseInit() {
    mst_sql_info.table_name = mst_cmd_config.proj_name.replace('-', '_') + "_optimize";  // table name cannt have '-'

    QStringList cols_name;
    cols_name << MyConnSql::mst_basic_cols_name.index_id + " INT AUTO_INCREMENT PRIMARY KEY" << MyConnSql::mst_basic_cols_name.date + " DATE"
              << MyConnSql::mst_basic_cols_name.time + " TIME" << MyConnSql::mst_basic_cols_name.chip_id + " VARCHAR(100)" << createDBColsName(10);

    MyConnSql::getInstance().mm_databases[MyConnSql::mst_accuracy_database.base_name]->createTable(mst_sql_info.table_name, cols_name);
}

// void CSprm::calibConfigUpdate(QVector<CSprm::StCalibItems>* v_calib_items_) {
//    StCalibItems calib_items_tmp;
//    uint8_t index = 0;
//    for(auto it = mi_spms_soc_->mm_calib_config.begin(); it != mi_spms_soc_->mm_calib_config.end(); it++) {
//        index++;
//        calib_items_tmp.angle = mm_nova_model_config["calib" + QString::number(index, 10) + "_angle"].toInt();
//        v_calib_items_->append(calib_items_tmp);
//    }
//}

/**
 * @brief CSprm::triggerStatusUpdate 获取模组触发输出方式：电平，指令
 * @param st_function_type
 */
// void CSprm::triggerStatusUpdate(StFunctionType &st_function_type) {
//    st_function_type.current_type = (ECurrentModule)mm_nova_model_config["current_monitor_dev"].toUInt();
//* read from pms device file
//}

void CSprm::processStatusUpdate() {
    QString config_file_name = QApplication::applicationDirPath() + "/config/device/pms/" + mst_cmd_config.proj_name + "_config.ini";

    CSprmReadIni::getInstance()->getProcessConfig(config_file_name, mst_process_config);

    m_calib_verify_tasks = {
        {EUniversalTasks::eTASK_SYSTEM_MODE, sc_processList.taskRegister(&CSprm::systemMode, true, 0, 0, 100, 5, false)},
        {EUniversalTasks::eTASK_CALIB, sc_processList.taskRegister(&CSprm::calibInfo, true, 0, 0, 0, 5, false)},
        {EUniversalTasks::eTASK_GET_DATA, sc_processList.taskRegister(&CSprm::getModuleData, true, 0, 0, 0, 5, false)},
        {EUniversalTasks::eTASK_OPTIMIZE, sc_processList.taskRegister(&CSprm::optimizationParamWrite, true, 250, 0, 200, 8, false)},
        {EUniversalTasks::eTASK_WORK, sc_processList.taskRegister(&CSprm::workInfo, true, 250, 0, 50, 5, false)},
    };
}

QMap<ISprmSoc::ECalibProcess, ISprmSoc::StCalibItemConfig> CSprm::getCalibItem() {
    return mi_spms_soc_->mm_calib_config;
}

QString CSprm::sensorVersionParse(const QByteArray &version) {
    QString tr_version;
    if (version.count() == 12) {
        tr_version = QString::number(version[8], 16) + "." + QString::number(version[9], 16) + "." + QString::number(version[10], 16) + "." +
                     QString::number(version[11], 16);
    } else
        tr_version = "version's lens not right: " + QString::number(version.count(), 10);
    return tr_version;
}

QMap<QString, QString> CSprm::projVersionInfoParse(const EProjVersionType &e_proj_version, const uint8_t *const info_, const uint8_t &len) {
    StPreviousProjVersion *st_previous_proj_version_ = nullptr;
    StNewProjVersion *     st_new_proj_version_      = nullptr;

    QMap<QString, QString> proj_version_parsed;
    switch (e_proj_version) {
    case EProjVersionType::ePREVIOUS_VERSION:
        st_previous_proj_version_ = (StPreviousProjVersion *)info_;
        proj_version_parsed.insert("project", CTypeConvert::changefromHex_to_ascii(st_previous_proj_version_->proj_series_name, 8));
        proj_version_parsed.insert("software version",
                                   QString::number(st_previous_proj_version_->sw_version[0], 10) + "." +
                                       QString::number(st_previous_proj_version_->sw_version[1], 10) + "." +
                                       QString::number(st_previous_proj_version_->sw_version[2], 10));
        proj_version_parsed.insert("hardware version", QString::number(st_previous_proj_version_->hw_version[0], 10));
        proj_version_parsed.insert("date",
                                   QString::number(st_previous_proj_version_->date[0], 10) + "." + QString::number(st_previous_proj_version_->date[1], 10) +
                                       "." + QString::number(st_previous_proj_version_->date[2], 10));
        break;

    case EProjVersionType::eNEW_VERSION:
        st_new_proj_version_ = (StNewProjVersion *)info_;
        proj_version_parsed.insert("project", CTypeConvert::changefromHex_to_ascii(st_new_proj_version_->series_name, 4));
        proj_version_parsed.insert("customID", CTypeConvert::changefromHex_to_ascii(st_new_proj_version_->custom_id, 3));
        proj_version_parsed.insert("software version",
                                   QString::number(st_new_proj_version_->sw_version[0], 10) + "." + QString::number(st_new_proj_version_->sw_version[1], 10) +
                                       "." + QString::number(st_new_proj_version_->sw_version[2], 10));
        proj_version_parsed.insert("hardware version", QString::number(st_new_proj_version_->hw_version[0], 10));
        proj_version_parsed.insert("date",
                                   QString::number(st_new_proj_version_->date[0], 10) + "." + QString::number(st_new_proj_version_->date[1], 10) + "." +
                                       QString::number(st_new_proj_version_->date[2], 10));
        proj_version_parsed.insert("info", QString::number(st_new_proj_version_->info[0], 10) + "." + QString::number(st_new_proj_version_->info[1], 10));
        break;

    default:
        break;
    }

    return proj_version_parsed;
}


uint8_t CSprm::getCalibTaskNum() {
    //    mv_calib_task_list.count();
}


EExecStatus CSprm::version(const bool &reexec, QVariant param) {
    static uint8_t reexec_flag = false, exec_cnt = 1;  //默认发送一次

    if (reexec || reexec_flag) {
        if (mst_port.port_->write(m_basic_cmd[ICommDevice::ECommDecBasicTasks::eTASK_VERSION])) {
            reexec_flag = false;

            CSerialThreadNode::getInstance().deviceSwitchInterface(mst_property.custom_property.instance_name, IProximitySensor::eCommType::eINTERACTION_TYPE);
            mm_comm_status[CSprm::sm_basic_tasks_name[ICommDevice::ECommDecBasicTasks::eTASK_VERSION].task_name].comm_status = IComm::ECommStatus::eCOMM_NONE;
            return eOK;
        } else
            return eERROR;
    }

    if (mm_comm_status[CSprm::sm_basic_tasks_name[ICommDevice::ECommDecBasicTasks::eTASK_VERSION].task_name].comm_status == IComm::ECommStatus::eCOMM_COMP) {
        exec_cnt    = 1;
        bool result = true;

        //* check version
        if ((!checkProject(mm_task_exec_info[DEV_PROJECT]))           // project name
            || (!checkVersion(mm_task_exec_info[DEV_SOFT_VERSION]))   // soft version
            || (!checkDate(mm_task_exec_info[DEV_FIRMWARE_DATE]))) {  // date
            result = false;
        }

        //            QString info = QString("project: " + mm_task_exec_info[DEV_PROJECT] +\
        //                                   "\nversion: " + mm_task_exec_info[DEV_SOFT_VERSION] +\
        //                                   "\ndate: " + mm_task_exec_info[DEV_FIRMWARE_DATE]
        //                                   );

        mm_task_exec_info.insert(DEV_TASK_RESULT, QString::number(result));

        return eCOMP;
    } else
        return eWAIT;
}

EExecStatus CSprm::chipId(const bool &reexec, QVariant param) {
    static uint8_t reexec_flag = false;  //默认发送一次

    if (reexec || reexec_flag) {
        if (mst_port.port_->write(m_basic_cmd[ICommDevice::ECommDecBasicTasks::eTASK_CHIP_ID])) {
            reexec_flag = false;

            CSerialThreadNode::getInstance().deviceSwitchInterface(mst_property.custom_property.instance_name, IProximitySensor::eCommType::eINTERACTION_TYPE);
            mm_comm_status[CSprm::sm_basic_tasks_name[ICommDevice::ECommDecBasicTasks::eTASK_CHIP_ID].task_name].comm_status = IComm::ECommStatus::eCOMM_NONE;
            return eOK;
        } else
            return eERROR;
    }

    if (mm_comm_status[CSprm::sm_basic_tasks_name[ICommDevice::ECommDecBasicTasks::eTASK_CHIP_ID].task_name].comm_status == IComm::ECommStatus::eCOMM_COMP) {
        mm_task_exec_info.insert(DEV_TASK_RESULT, QString::number(1));
        mst_sql_info.curr_chip_id = mm_task_exec_info[DEV_CHIP_ID];
        return eCOMP;
    } else
        return eWAIT;
}

EExecStatus CSprm::systemMode(const bool &reexec, QVariant param) {
    static uint8_t reexec_flag = false;  //默认发送一次

    if (reexec || reexec_flag) {
        // get system mode cmd
        QString cmd_name = getCmdName(CTypeConvert::enumToString(m_id_Enum, (uint16_t)EPmsAllId::ePROC_MODE),
                                      CTypeConvert::enumToString(s_system_mode_Enum, uint16_t(param.toUInt())));

        auto       it  = mst_protocol_comm.interaction_cmd.find(cmd_name);
        QByteArray cmd = mst_protocol_comm.interaction_cmd.begin().value();
        if (it != mst_protocol_comm.interaction_cmd.end()) {
            cmd = it.value();
        }

        // send cmd
        if (mst_port.port_->write(cmd)) {
            reexec_flag = false;

            CSerialThreadNode::getInstance().deviceSwitchInterface(mst_property.custom_property.instance_name, IProximitySensor::eCommType::eINTERACTION_TYPE);
            mm_comm_status[CSprm::sm_universal_tasks_name[EUniversalTasks::eTASK_SYSTEM_MODE].task_name].comm_status = IComm::ECommStatus::eCOMM_NONE;
            return eOK;
        } else
            return eERROR;
    }

    if (mm_comm_status[CSprm::sm_universal_tasks_name[EUniversalTasks::eTASK_SYSTEM_MODE].task_name].comm_status == IComm::ECommStatus::eCOMM_COMP) {
        return eCOMP;
    } else
        return eWAIT;
}

// EExecStatus CSprm::analysisMode(const bool &reexec, QVariant param) {
//     Q_UNUSED(param)
//     static uint8_t reexec_flag = false;  //默认发送一次

//     if (reexec || reexec_flag) {
//         if (mst_port.port_->write(m_calib_verify_cmd[IProximitySensor::EUniversalTasks::eTASK_ANALYSIS_MODE])) {
//             reexec_flag = false;

//             CSerialThreadNode::getInstance().deviceSwitchInterface(mst_property.custom_property.instance_name,
//             IProximitySensor::eCommType::eINTERACTION_TYPE);
//             mm_comm_status[CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_ANALYSIS_MODE].task_name].comm_status =
//                 IComm::ECommStatus::eCOMM_NONE;
//             return eOK;
//         } else
//             return eERROR;
//     }

//     if (mm_comm_status[CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_ANALYSIS_MODE].task_name].comm_status ==
//         IComm::ECommStatus::eCOMM_COMP) {
//         return eCOMP;
//     } else
//         return eWAIT;
// }

// EExecStatus CSprm::optimizeParamWriteMode(const bool &reexec, QVariant param) {
//     Q_UNUSED(param)
//     static uint8_t reexec_flag = false;  //默认发送一次

//     if (reexec || reexec_flag) {
//         if (mst_port.port_->write(m_calib_verify_cmd[IProximitySensor::EUniversalTasks::eTASK_OPTIMIZE_MODE])) {
//             reexec_flag = false;

//             CSerialThreadNode::getInstance().deviceSwitchInterface(mst_property.custom_property.instance_name,
//             IProximitySensor::eCommType::eINTERACTION_TYPE);
//             mm_comm_status[CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_OPTIMIZE_MODE].task_name].comm_status =
//                 IComm::ECommStatus::eCOMM_NONE;
//             return eOK;
//         } else {
//             return eERROR;
//         }
//     }

//     if (mm_comm_status[CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_OPTIMIZE_MODE].task_name].comm_status ==
//         IComm::ECommStatus::eCOMM_COMP) {
//         return eCOMP;
//     } else
//         return eWAIT;
// }

// EExecStatus CSprm::verifyMode(const bool &reexec, QVariant param) {
//     Q_UNUSED(param)
//     static uint8_t reexec_flag = false;  //默认发送一次

//     if (reexec || reexec_flag) {
//         if (mst_port.port_->write(m_calib_verify_cmd[IProximitySensor::EUniversalTasks::eTASK_VERIFY_MODE])) {
//             reexec_flag = false;

//             CSerialThreadNode::getInstance().deviceSwitchInterface(mst_property.custom_property.instance_name,
//             IProximitySensor::eCommType::eINTERACTION_TYPE);
//             mm_comm_status[CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_VERIFY_MODE].task_name].comm_status =
//                 IComm::ECommStatus::eCOMM_NONE;
//             return eOK;
//         } else
//             return eERROR;
//     }

//     if (mm_comm_status[CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_VERIFY_MODE].task_name].comm_status ==
//         IComm::ECommStatus::eCOMM_COMP) {
//         return eCOMP;
//     } else
//         return eWAIT;
// }

// EExecStatus CSprm::workMode(const bool &reexec, QVariant param) {
//     static uint8_t reexec_flag = false, exec_cnt = 0;

//     if (reexec || reexec_flag) {
//         if (mst_port.port_->write(m_calib_verify_cmd[IProximitySensor::EUniversalTasks::eTASK_WORK_MODE])) {
//             reexec_flag = false;

//             CSerialThreadNode::getInstance().deviceSwitchInterface(mst_property.custom_property.instance_name,
//             IProximitySensor::eCommType::eINTERACTION_TYPE);
//             mm_comm_status[CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_WORK_MODE].task_name].comm_status =
//                 IComm::ECommStatus::eCOMM_NONE;
//             return eOK;
//         } else
//             return eERROR;
//     }

//     if (mm_comm_status[CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_WORK_MODE].task_name].comm_status ==
//         IComm::ECommStatus::eCOMM_COMP) {
//         if (++exec_cnt < param.toUInt()) {
//             reexec_flag = true;
//             return eOK;
//         } else {
//             exec_cnt = 0;

//             //* handle distance
//             mm_task_exec_info.insert(CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_WORK_MODE].task_name,
//                                      QString::number(mst_used_data.distance.getResult().value_aver, 10));

//             return eCOMP;
//         }
//     } else
//         return eWAIT;
// }

EExecStatus CSprm::calibInfo(const bool &reexec, QVariant param) {
    Q_UNUSED(reexec)
    Q_UNUSED(param)
    EExecStatus status = mc_calib_process_->tasksRun(this, &mv_calib_task_list, mst_calib_task_status_);

    if (status == eCOMP || status == ePROCESS_COMP) {
        return eCOMP;  //单步校正完成
    } else if (status == eFATAL) {
        return eFATAL;
    }
    return eWAIT;
}

// EExecStatus CSprm::analysisData(const bool &reexec, QVariant param) {
//     static uint8_t reexec_flag = false, exec_cnt = 0;  //默认发送一次
//     QStringList    params = param.toString().split(',');
//     if (params.count() < 2) {
//         qWarning() << "CSprm/"
//                    << "analysis param num error: least 2, but only has %1. %2" << params.count() << param.toString();
//         return eERROR;
//     }
//     m_current_distance = params.at(1).toUShort();
//     m_is_ack           = true;

//     if (reexec || reexec_flag) {
//         reexec_flag = false;

//         CSerialThreadNode::getInstance().deviceSwitchInterface(mst_property.custom_property.instance_name, IProximitySensor::eCommType::eANALYSIS_TYPE);
//         mm_comm_status[CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_ANALYSIS_DATA].task_name].comm_status =
//             IComm::ECommStatus::eCOMM_NONE;
//     }

//     if (mm_comm_status[CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_ANALYSIS_DATA].task_name].comm_status ==
//         IComm::ECommStatus::eCOMM_COMP) {
//         if (++exec_cnt < params.at(0).toUInt()) {
//             reexec_flag = true;
//             return eOK;
//         } else {
//             exec_cnt = 0;

//             //* handle distance
//             mm_task_exec_info.insert(CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_ANALYSIS_DATA].task_name,
//                                      QString::number(mst_used_data.distance.getResult(0).value_aver, 10));

//             return eCOMP;
//         }
//     } else
//         return eWAIT;
// }

/**
 * @brief 获取测距模组数据，包括距离，强度，等等
 *
 * @param reexec
 * @param param
 * @return EExecStatus
 */
EExecStatus CSprm::getModuleData(const bool &reexec, QVariant param) {
    static uint8_t reexec_flag = false, exec_cnt = 0;

    if (reexec || reexec_flag) {
        mm_comm_status[CSprm::sm_universal_tasks_name[EUniversalTasks::eTASK_GET_DATA].task_name].comm_status = IComm::ECommStatus::eCOMM_NONE;

        if (mst_port.port_->write(m_calib_verify_cmd[EUniversalTasks::eTASK_GET_DATA])) {
            reexec_flag = false;

            CSerialThreadNode::getInstance().deviceSwitchInterface(mst_property.custom_property.instance_name, IProximitySensor::eCommType::eINTERACTION_TYPE);
            return eOK;
        } else
            return eERROR;
    }

    if (mm_comm_status[CSprm::sm_universal_tasks_name[EUniversalTasks::eTASK_GET_DATA].task_name].comm_status == IComm::ECommStatus::eCOMM_COMP) {
        if (++exec_cnt < param.toUInt()) {
            reexec_flag = true;
            return eOK;
        } else {
            exec_cnt = 0;

            //* handle distance
            mm_task_exec_info.insert(CSprm::sm_universal_tasks_name[EUniversalTasks::eTASK_GET_DATA].task_name,
                                     QString::number(mst_used_data.distance.getResult(1).value_aver, 10));

            return eCOMP;
        }
    } else
        return eWAIT;
}

EExecStatus CSprm::optimizationParamWrite(const bool &reexec, QVariant param) {
    Q_UNUSED(param)
    static uint8_t    reexec_flag = false;  //默认发送一次
    static QByteArray s_tmp;

    if (reexec || reexec_flag) {
        if (reexec) {  //首次执行
            if (!getOptimizeCmd(s_tmp))
                return eFATAL;

            mst_sql_info.optimized_chip_id = mst_sql_info.curr_chip_id;
        }
        qDebug() << "CSprm/ "
                 << "optimizationParam cmd: " << CTypeConvert::byteArrayToHex(s_tmp);
        if (mst_port.port_->write(s_tmp)) {
            reexec_flag = false;

            CSerialThreadNode::getInstance().deviceSwitchInterface(mst_property.custom_property.instance_name, IProximitySensor::eCommType::eINTERACTION_TYPE);
            mm_comm_status[CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_OPTIMIZE].task_name].comm_status =
                IComm::ECommStatus::eCOMM_NONE;

            return eOK;
        } else {
            return eERROR;
        }
    }

    if (mm_comm_status[CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_OPTIMIZE].task_name].comm_status ==
        IComm::ECommStatus::eCOMM_COMP) {
        mm_task_exec_info = m_factor_show;

        cleanOptimizeData();
        return eCOMP;
    } else
        return eWAIT;
}

EExecStatus CSprm::workInfo(const bool &reexec, QVariant param) {
    static uint8_t reexec_flag = false, exec_cnt = 0;  //默认发送一次

    if (reexec || reexec_flag) {
        if (mst_port.port_->write(m_calib_verify_cmd[IProximitySensor::EUniversalTasks::eTASK_WORK])) {
            reexec_flag = false;

            CSerialThreadNode::getInstance().deviceSwitchInterface(mst_property.custom_property.instance_name, IProximitySensor::eCommType::eINTERACTION_TYPE);
            mm_comm_status[CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_WORK].task_name].comm_status =
                IComm::ECommStatus::eCOMM_NONE;
            return eOK;
        } else
            return eERROR;
    }

    if (mm_comm_status[CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_WORK].task_name].comm_status == IComm::ECommStatus::eCOMM_COMP) {
        if (++exec_cnt <= param.toUInt()) {
            reexec_flag = true;

            return eOK;
        } else {
            exec_cnt = 0;

            //* handle distance
            mm_task_exec_info.insert(CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_WORK].task_name,
                                     QString::number(mst_used_data.distance.getResult().value_aver, 10));
            mm_task_exec_info.insert(PMS_CONFIDENCE, QString::number(mst_used_data.confidence.getResult().value_aver));

            return eCOMP;
        }
    } else
        return eWAIT;
}

EExecStatus CSprm::readDds(const bool &reexec, QVariant param) {
    static uint8_t reexec_flag = false, exec_cnt = 0;  //默认发送一次

    if (reexec || reexec_flag) {
        if (mst_port.port_->write(m_basic_cmd[ICommDevice::ECommDecBasicTasks::eTASK_READ_DDS])) {
            reexec_flag = false;

            CSerialThreadNode::getInstance().deviceSwitchInterface(mst_property.custom_property.instance_name, IProximitySensor::eCommType::eINTERACTION_TYPE);
            mm_comm_status[CSprm::sm_basic_tasks_name[ICommDevice::ECommDecBasicTasks::eTASK_READ_DDS].task_name].comm_status = IComm::ECommStatus::eCOMM_NONE;
            return eOK;
        } else
            return eERROR;
    }

    if (mm_comm_status[CSprm::sm_basic_tasks_name[ICommDevice::ECommDecBasicTasks::eTASK_READ_DDS].task_name].comm_status == IComm::ECommStatus::eCOMM_COMP) {
        if (++exec_cnt < param.toUInt()) {
            reexec_flag = true;
            return eOK;
        } else {
            exec_cnt = 0;

            bool result = true;

            //* check dds
            if (mm_task_exec_info[DEV_DDS].toUShort() != 0) {  // date
                result = false;
            }
            mm_task_exec_info.insert(DEV_TASK_RESULT, QString::number(result));

            return eCOMP;
        }
    } else
        return eWAIT;
}

bool CSprm::proj51DataOutputMode(EDataOutputMode output_mode) {
    switch (output_mode) {
    case eNOT_OUTPUT_MODE:
        return mst_port.port_->write(mst_protocol_comm.interaction_cmd["eDATA_MODE_eNOT_OUTPUT_MODE"]);
        break;

    case eCURVE_SHOW_OUTPUT_MODE:
        return mst_port.port_->write(mst_protocol_comm.interaction_cmd["eDATA_MODE_eCURVE_SHOW_OUTPUT_MODE"]);
        break;

    case eDISTANCE_OUTPUT_MODE:
        return mst_port.port_->write(mst_protocol_comm.interaction_cmd["eDATA_MODE_eDISTANCE_OUTPUT_MODE"]);
        break;

    default:
        return mst_port.port_->write(mst_protocol_comm.interaction_cmd["eDATA_MODE_eNOT_OUTPUT_MODE"]);
        break;
    }
}

bool CSprm::ledMode(const ELedMode &led_mode) {
    // switch (led_mode) {
    // case ELedMode::eCLOSE:
    //     return mst_port.port_->write(mst_protocol_comm.interaction_cmd["eLED_IO_kCLOSE"]);
    //     break;
    // case ELedMode::eOPEN:
    //     return mst_port.port_->write(mst_protocol_comm.interaction_cmd["eLED_IO_kOPEN"]);
    //     break;
    // case ELedMode::eBREATH:
    //     return mst_port.port_->write(mst_protocol_comm.interaction_cmd["eLED_IO_kBREATH"]);
    //     break;
    // case ELedMode::eFLICKER:
    //     return mst_port.port_->write(mst_protocol_comm.interaction_cmd["eLED_IO_kFLICKER"]);
    //     break;
    // default:
    //     return mst_port.port_->write(mst_protocol_comm.interaction_cmd["eLED_IO_kCLOSE"]);
    //     break;
    // }
}

bool CSprm::systemMode(const ESysMode &sys_mode) {
    // switch (sys_mode) {
    // case ESysMode::eTEST_MODE:
    //     return mst_port.port_->write(mst_protocol_comm.interaction_cmd["eMODE_MODIFY_KTEST_MODE"]);
    //     break;

    // case ESysMode::eHISTOGRAM_MODE:
    //     return mst_port.port_->write(mst_protocol_comm.interaction_cmd["eMODE_MODIFY_kCALIB_MODE"]);
    //     break;

    // case ESysMode::eCALIBRATION_MODE:
    //     return mst_port.port_->write(mst_protocol_comm.interaction_cmd["eMODE_MODIFY_kCALIB_MODE"]);
    //     break;

    // case ESysMode::eOPTIMIZATION_MODE:
    //     return mst_port.port_->write(mst_protocol_comm.interaction_cmd["eMODE_MODIFY_KOPTIMIZATION_MODE"]);
    //     break;

    // case ESysMode::eCONTINUE_RANGE_MODE:
    //     return mst_port.port_->write(mst_protocol_comm.interaction_cmd["eMODE_MODIFY_kMEASURE_MODE"]);
    //     break;

    // case ESysMode::eSINGLE_RANGE_MODE:
    //     return mst_port.port_->write(mst_protocol_comm.interaction_cmd["eMODE_MODIFY_kMEASURE_MODE"]);
    //     break;

    // case ESysMode::eSINGLE_HIST_MIX_MODE:
    //     return mst_port.port_->write(mst_protocol_comm.interaction_cmd["eMODE_MODIFY_kTRIGGER_MODE"]);
    //     break;

    // case ESysMode::eSTANDBY_MODE:
    //     return mst_port.port_->write(mst_protocol_comm.interaction_cmd["eMODE_MODIFY_kSTANDBY_MODE"]);
    //     break;

    // case ESysMode::eWORK_MODE:
    //     return mst_port.port_->write(mst_protocol_comm.interaction_cmd["eMODE_MODIFY_kTRIGGER_MODE"]);
    //     break;

    // default:
    //     break;
    // }
}

bool CSprm::selfAdapt() {
    return mst_port.port_->write(mst_protocol_comm.interaction_cmd["eSELF_ADAPT"]);
}

bool CSprm::standbyMode() {
    return mst_port.port_->write(mst_protocol_comm.interaction_cmd["eMODE_MODIFY_kSTANDBY_MODE"]);
}

EExecStatus CSprm::calibTasksRun() {
    EExecStatus status = mc_calib_process_->tasksRun(this, &mv_calib_task_list, mst_calib_task_status_);
    //    if((mst_calib_task_status_->cur_status == eCOMP) && (uint8_t)mst_calib_task_status_->cur_step + 1 == mv_calib_task_list.size()) {
    //        return eTASK_COMP;
    //    }
    return status;
}


EExecStatus CSprm::calibTask1() {
    if (mst_port.port_->write(mst_protocol_comm.interaction_cmd.value("eCALIB1"))) {
        // qDebug() << "-i uart/ write data:" << CTypeConvert::byteArrayToHex(mm_calib_flow.value(ISprmSoc::ECalibStep::eCALIB1).cmd);
        CSerialThreadNode::getInstance().deviceSwitchInterface(mst_property.custom_property.instance_name, ICommDevice::eCommType::eINTERACTION_TYPE);

        m_calib_mode = ECalibMode::eXTALK;
        return eOK;
    } else
        return eFATAL;
    // return mi_spms_soc_->calibTask1();
}

EExecStatus CSprm::calibTask1Ack() {
    if (mst_comm_status_->comm_status == IComm::ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == IComm::ECommStatus::eCOMM_COMP) {

//        emit calibValueAckSignal(1, param1, (uint16_t)param2);
#if 0
        bool result = true;
        if(mm_calib_flow.value(ISprmSoc::ECalibStep::eCALIB1).calib_items.param1_value > mm_calib_flow.value(ISprmSoc::ECalibStep::eCALIB1).calib_items.param1) {
            result = false;
        }
        qDebug() << mm_calib_flow[ISprmSoc::ECalibStep::eCALIB1].calib_items.param1;
        //* 判定 peak
        if(mm_calib_flow.value(ISprmSoc::ECalibStep::eCALIB1).calib_items.param2_value < mm_calib_flow.value(ISprmSoc::ECalibStep::eCALIB1).calib_items.param2) {
            result = false;
        }
        mm_calib_flow[ISprmSoc::ECalibStep::eCALIB1].calib_items.result = result;
#elif 0
        //* 判定 peak
        if (mv_calib_items_->at(0).param2_value > mv_calib_items_->at(0).param2 || mv_calib_items_->at(0).param2_value == 0) {
            result = false;
        }
        //        qDebug() << "-i pmsA1C/ param2:" << mv_calib_items_->at(0).param2_value << mv_calib_items_->at(0).param2;
        (*mv_calib_items_)[0].result = result;
#else
        //* check value
        bool result = mi_spms_soc_->calibItemCheck(ISprmSoc::eCALIB1);  // mst_iniConfig.judge_method,
        //* show info
        mm_calib_params_show.insert(proximity_sensor::calib_result, QString::number(result));
        emit calibShow(eCALIB1_STEP, mm_calib_params_show);
#endif
        return eCOMP;
        //        return eWAIT;
    } else if (mst_comm_status_->comm_status == IComm::ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
}

EExecStatus CSprm::calibTask2() {
    if (mst_port.port_->write(mst_protocol_comm.interaction_cmd.value("eCALIB2"))) {
        m_calib_mode = ECalibMode::eOFFSET;

        // m_serial_thread_->task_id_change(3);
        return eOK;
    } else
        return eFATAL;
}

EExecStatus CSprm::calibTask2Ack() {
    if (mst_comm_status_->comm_status == IComm::ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == IComm::ECommStatus::eCOMM_COMP) {
#if 0
        bool result = true;
//        if(mm_calib_flow.value(ISprmSoc::ECalibStep::eCALIB2).calib_items.param1_value > mm_calib_flow.value(ISprmSoc::ECalibStep::eCALIB1).calib_items..param1) {
//            result = false;
//        }

        //* 判定 peak
//        if(mm_calib_flow.value(ISprmSoc::ECalibStep::eCALIB2).calib_items.param2_value < mm_calib_flow.value(ISprmSoc::ECalibStep::eCALIB1).calib_items..param2) {
//            result = false;
//        }
        mm_calib_flow[ISprmSoc::ECalibStep::eCALIB2].calib_items.result = result;
#else
        //* check value
        bool result = mi_spms_soc_->calibItemCheck(ISprmSoc::eCALIB2);  // mst_iniConfig.judge_method,
        //* show info
        mm_calib_params_show.insert(proximity_sensor::calib_result, QString::number(result, 10));
        emit calibShow(eCALIB2_STEP, mm_calib_params_show);
#endif

        return eCOMP;
    } else if (mst_comm_status_->comm_status == IComm::ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
}

EExecStatus CSprm::calibTask3() {
    //    return mi_spms_soc_->calibTask3();
}

EExecStatus CSprm::calibTask3Ack() {
}

EExecStatus CSprm::calibTask4() {
}

EExecStatus CSprm::calibTask4Ack() {
}

// bool CSprm::getQueryDist() {
//    return mst_port.port_->write(
//        mst_protocol_comm.interaction_protocol_->getWriteCmd((uint8_t)proximity_sensor::eQUERY_DIST,
//        CTypeConvert::enumToByteArray(static_cast<uint8_t>(0x00))));
//    //    return true; //auto send
//}

// bool CSprm::readVersion() {
//    return mst_port.port_->write(mst_protocol_comm.interaction_protocol_->getControlCmd((uint8_t)proximity_sensor::eVERSION));  //
//}

// bool CSprm::readChipID() {
//    return mst_port.port_->write(mst_protocol_comm.interaction_protocol_->getControlCmd((uint8_t)proximity_sensor::eCHIP_ID));  //
//}

// bool CSprm::readDds() {
//    return mst_port.port_->write(mst_protocol_comm.interaction_protocol_->getControlCmd((uint8_t)proximity_sensor::eDDS_LOG));  //
//}

void CSprm::taskInit() {
    mc_calib_process_->taskInit(&mv_calib_task_list, mst_calib_task_status_);

    mst_calib_task_status_->cur_step    = eCALIB1_STEP;
    mst_calib_task_status_->cur_status  = eWAIT;
    mst_calib_task_status_->last_step   = eCALIB1_STEP;
    mst_calib_task_status_->last_status = eWAIT;

    //    m_cur_angle = mm_calib_param["calib1_angle"].toUInt();

    mv_calib_task_list[ECalibStep::eCALIB1_STEP].flag.exec = true;  // start calib
}

QString CSprm::getDdsInfo(const uint16_t &dds, const uint8_t &dym_dds) {
    QString error_info = "";
    for (uint16_t for_i = 0; for_i < 16; for_i++) {
        uint16_t error_index = dds & (1 << for_i);
        if (error_index != 0)
            error_info += QString::number(for_i, 10) + "." + mi_spms_soc_->mm_soc_dds_describe[error_index] + "\n";
    }
    return error_info;
}

bool CSprm::getOptimizeCmd(QByteArray &cmd) {
    return true;
}

CSprm::process_type::StTaskInfo CSprm::getTaskConfig(const uint16_t &task_id) {
    if (task_id < (uint16_t)IProximitySensor::ECommDecBasicTasks::eLAST) {
        return sm_basic_tasks[(IProximitySensor::ECommDecBasicTasks)task_id];
    } else if (task_id < (uint16_t)IProximitySensor::EUniversalTasks::eCALIB_VERIFY_LAST) {
        return m_calib_verify_tasks[(IProximitySensor::EUniversalTasks)task_id];
    } else {
        return sm_basic_tasks[(IProximitySensor::ECommDecBasicTasks)0];
    }
}

bool CSprm::getResult() {
    bool result = true;

    //* calib result
    result &= mi_spms_soc_->getResult(ISprmSoc::eCALIB1, true);

    return result;
}

/**
 * @brief spad 饱和值，用于pileup计算
 * @param reduce_peak
 * @param integral_cnt
 * @return
 */
inline double CSprm::peakRatio(const uint &reduce_peak, const uint &integral_cnt) {
    return reduce_peak * 16 / (double)integral_cnt;
}

/**
 * @brief CSprm::tof_del
 * @param ori_tof
 * @param act_dist
 * @return ori_tof - act_dist
 */
inline int16_t CSprm::tofDelta(const int16_t &ori_tof, const int &act_dist) {
    return act_dist - ori_tof;
}


EExecStatus CSprm::singleTaskRunning(const StDevTaskInfo &task_info,
                                     const bool &new_task) {  //, NProcessTemplateC::StTaskExec &task_exec, NProcessTemplateC::StStepRecord &step_record
    //    EExecStatus status = sc_processList.tasksRun(this, sm_tasks[task_info.task_name], &mst_task_exec, &mst_step_record, task_info.param);

    //* new task, clean
    if (new_task) {
        moduleUsedDataClean();

        mm_task_exec_info.clear();
    }

    const process_type::StTaskInfo task   = getTaskConfig(task_info.task_id);
    EExecStatus                    status = sc_processList.tasksRun(this, task, &mst_task_exec, &mst_step_record, task_info.param);
    if (status == eCOMP) {
        taskExecVarInit();

        //        emit commTaskInfoShow(mm_task_exec_info);
    } else if (status == eFATAL) {
        return eFATAL;
    }
    return status;
}

EExecStatus CSprm::tasksProcessRunning() {
    //* 当前任务执行->等待ack？
    //    EExecStatus status = sc_processList.tasksRun(this, sm_tasks[vst_dev_tasks.at(m_task_index).task_info.task_name], &mst_task_exec, &mst_step_record,
    //    0);

    const process_type::StTaskInfo task   = getTaskConfig(vst_dev_tasks.at(m_task_index).task_info.task_id);
    EExecStatus                    status = sc_processList.tasksRun(this, task, &mst_task_exec, &mst_step_record, 0);
    if (status == eCOMP) {
        //* 执行下一级任务
        taskExecVarInit();

        m_task_index++;
        this->st_next_dev_task.dev_p_ = vst_dev_tasks.at(m_task_index).dev_p_;
        return deviceProc::tasksProcessRunning();
    }

    return status;
}

EExecStatus CSprm::tasksProcessRunning(NProcessTemplateC::StTaskExec &task_exec, NProcessTemplateC::StStepRecord &step_record) {
    //* 当前任务执行->等待ack？
    const process_type::StTaskInfo task = getTaskConfig(vst_dev_tasks.at(m_task_index).task_info.task_id);
    if (sc_processList.tasksRun(this, task, &task_exec, &step_record, 0) == eCOMP) {
        //* 执行下一级任务
        m_task_index++;

        this->st_next_dev_task.dev_p_ = vst_dev_tasks.at(m_task_index).dev_p_;
        return deviceProc::tasksProcessRunning(task_exec, step_record);
    }
    return eWAIT;
}

bool CSprm::analysisDataParsing(QByteArray &            str,
                                const int &             length,
                                QByteArray &            complete_frame,  // eCmdParseMode mode,
                                QMap<QString, QString> &complete_frame_parsed,
                                QMap<QString, QString> &auto_show_frame) {
    Q_UNUSED(length);

    QByteArray strSum = mst_protocol_comm.strPre + str;
    str.clear();

    LOG_INFO(MyLogger::LogType::COMM_ACK, QString("analysis data: %1").arg(CTypeConvert::byteArrayToHex(strSum)));

    //    QMetaEnum stepEnum = QMetaEnum::fromType<CPmsA1C::EProtocolId>();
    //    = reinterpret_cast<CModel_pmsCurveP::StInteractionFrame *>(complete_frame.data());

    if (!mst_protocol_comm.analysis_protocol_->interactionParing(strSum, complete_frame, complete_frame_parsed)) {
        mst_protocol_comm.strPre = strSum;
        return false;
    }

    CModel_pmsCurveP::StInteractionFrame *data_receive_ =
        dynamic_cast<CModel_pmsCurveP::StInteractionFrame *>(mst_protocol_comm.analysis_protocol_->getInteractionFrame(complete_frame.data()));

    //* 执行步骤ack output
    if (data_receive_->header == (uint16_t)CModel_pmsCurveP::EInteractionFrame::eHEADER_ACK) {  // bester 距离返回ACK协议不一样（无cmd id）->特殊处理
        if (m_is_ack) {
            m_is_ack = false;

            QStringList all_name;
            all_name << mm_interval_fitting.first().interval_dym_config.input_names << mm_interval_fitting.first().interval_dym_config.output_name
                     << mm_interval_fitting.first().interval_dym_config.interval_name;

            if (m_is_sample) {  // sample mode
#if 0
                //* 显示与拟合有关的数据。。。如果一个点用于多段，且每段所需数据items不同，如何处理
                if(mm_interval_fitting.first().interval_dym_config.input_names.contains(PMS_ORI_DIST)) {
                    mm_optimize_data[50].origin_dist.addData(data_receive_->ori_dist);
                    auto_show_frame.insert(PMS_ORI_DIST, QString::number(data_receive_->ori_dist, 10));
                }
                //            else if(mm_interval_fitting.first().interval_dym_config.input_names.contains(PMS_SOC_DIST_MM)) {
                //                mm_optimize_data[50].distance.addData(data_receive_->soc_dist);
                //                auto_show_frame.insert(PMS_SOC_DIST_MM, QString::number(data_receive_->soc_dist, 10));
                //            }
                else if(mm_interval_fitting.first().interval_dym_config.input_names.contains(PMS_DIST_MM)) {
                    mm_optimize_data[50].distance.addData(data_receive_->corr_dist);
                    auto_show_frame.insert(PMS_DIST_MM, QString::number(data_receive_->corr_dist, 10));
                }
                else if(mm_interval_fitting.first().interval_dym_config.input_names.contains(PMS_INTE_CNT)) {
                    mm_optimize_data[50].integral_cnt.addData(data_receive_->integral_times);
                    auto_show_frame.insert(PMS_INTE_CNT, QString::number(data_receive_->integral_times, 10));
                }
                else if(mm_interval_fitting.first().interval_dym_config.input_names.contains(PMS_PEAK)) {
                    mm_optimize_data[50].peak1.addData(data_receive_->peak1);
                    auto_show_frame.insert(PMS_PEAK, QString::number(data_receive_->peak1, 10));
                }
                else if(mm_interval_fitting.first().interval_dym_config.input_names.contains(PMS_REDUCE_PEAK))  {
                    mm_optimize_data[50].peak_reduce.addData(data_receive_->peak2);
                    auto_show_frame.insert(PMS_REDUCE_PEAK, QString::number(data_receive_->peak2, 10));
                }
                else if(mm_interval_fitting.first().interval_dym_config.input_names.contains(PMS_NOISE)) {
                    mm_optimize_data[50].peak_reduce.addData(data_receive_->noise);
                    auto_show_frame.insert(PMS_NOISE, QString::number(data_receive_->noise, 10));
                }
#elif 1
                //* check ack frame has all optimization needed item, give error info if lost
                //                for(auto opt_it = all_name.begin(); opt_it != all_name.end(); opt_it++) {
                //                   if()
                //                }

                //* 显示与拟合有关的数据。。。如果一个点用与多段，且每段所需数据items不同，如何处理
                if (all_name.contains(PMS_ORI_DIST)) {
                    mv_autoSample_points[m_current_distance][PMS_ORI_DIST].addData(data_receive_->ori_dist);
                    auto_show_frame.insert(PMS_ORI_DIST, QString::number(data_receive_->ori_dist, 10));
                }
                if (all_name.contains(PMS_DIST_MM)) {
                    mv_autoSample_points[m_current_distance][PMS_INTE_CNT].addData(data_receive_->corr_dist);
                    auto_show_frame.insert(PMS_DIST_MM, QString::number(data_receive_->corr_dist, 10));
                }
                if (all_name.contains(PMS_INTE_CNT)) {
                    mv_autoSample_points[m_current_distance][PMS_INTE_CNT].addData(data_receive_->integral_times);
                    auto_show_frame.insert(PMS_INTE_CNT, QString::number(data_receive_->integral_times, 10));
                }
                if (all_name.contains(PMS_PEAK)) {
                    mv_autoSample_points[m_current_distance][PMS_PEAK].addData(data_receive_->peak1);
                    auto_show_frame.insert(PMS_PEAK, QString::number(data_receive_->peak1, 10));
                }
                if (all_name.contains(PMS_REDUCE_PEAK)) {
                    mv_autoSample_points[m_current_distance][PMS_REDUCE_PEAK].addData(data_receive_->peak2);
                    auto_show_frame.insert(PMS_REDUCE_PEAK, QString::number(data_receive_->peak2, 10));
                }
                if (all_name.contains(PMS_NOISE)) {
                    mv_autoSample_points[m_current_distance][PMS_NOISE].addData(data_receive_->noise);
                    auto_show_frame.insert(PMS_NOISE, QString::number(data_receive_->noise, 10));
                }
                if (all_name.contains(PMS_REDUCE_PEAK_RATIO)) {
                    double reduce_peak_ratio = peakRatio(data_receive_->peak2, data_receive_->integral_times);
                    mv_autoSample_points[m_current_distance][PMS_REDUCE_PEAK_RATIO].addData(reduce_peak_ratio);
                    auto_show_frame.insert(PMS_REDUCE_PEAK_RATIO, QString::number(reduce_peak_ratio, 'f', 3));
                }
                if (all_name.contains(PMS_TOF_DELTA)) {
                    int16_t tof_delta = tofDelta(data_receive_->ori_dist, m_current_distance);
                    mv_autoSample_points[m_current_distance][PMS_TOF_DELTA].addData(tof_delta);
                    auto_show_frame.insert(PMS_TOF_DELTA, QString::number(tof_delta, 10));
                }
#endif
            } else {  // analysis mode
                //* receive data
                auto_show_frame.insert(PMS_ORI_DIST, QString::number(data_receive_->ori_dist, 10));
                auto_show_frame.insert(PMS_SOC_DIST_MM, QString::number(data_receive_->soc_dist, 10));
                auto_show_frame.insert(PMS_DIST_MM, QString::number(data_receive_->corr_dist, 10));
                auto_show_frame.insert(PMS_INTE_CNT, QString::number(data_receive_->integral_times, 10));
                auto_show_frame.insert(PMS_PEAK, QString::number(data_receive_->peak1, 10));
                auto_show_frame.insert(PMS_REDUCE_PEAK, QString::number(data_receive_->peak2, 10));
                auto_show_frame.insert(PMS_NOISE, QString::number(data_receive_->noise, 10));
                auto_show_frame.insert(PMS_CONFIDENCE, QString::number(data_receive_->confident, 10));

                //* 显示与拟合有关的数据。。。如果一个点用与多段，且每段所需数据items不同，如何处理
                if (all_name.contains(PMS_ORI_DIST)) {
                    mm_autoVerify_cur_point[PMS_ORI_DIST].addData(data_receive_->ori_dist);
                }
                if (all_name.contains(PMS_DIST_MM)) {
                    mm_autoVerify_cur_point[PMS_INTE_CNT].addData(data_receive_->corr_dist);
                }
                if (all_name.contains(PMS_INTE_CNT)) {
                    mm_autoVerify_cur_point[PMS_INTE_CNT].addData(data_receive_->integral_times);
                }
                if (all_name.contains(PMS_PEAK)) {
                    mm_autoVerify_cur_point[PMS_PEAK].addData(data_receive_->peak1);
                }
                if (all_name.contains(PMS_REDUCE_PEAK)) {
                    mm_autoVerify_cur_point[PMS_REDUCE_PEAK].addData(data_receive_->peak2);
                }
                if (all_name.contains(PMS_NOISE)) {
                    mm_autoVerify_cur_point[PMS_NOISE].addData(data_receive_->noise);
                }
                if (all_name.contains(PMS_REDUCE_PEAK_RATIO)) {
                    double reduce_peak_ratio = peakRatio(data_receive_->peak2, data_receive_->integral_times);
                    mm_autoVerify_cur_point[PMS_REDUCE_PEAK_RATIO].addData(reduce_peak_ratio);
                    auto_show_frame.insert(PMS_REDUCE_PEAK_RATIO, QString::number(reduce_peak_ratio, 'f', 3));
                }
                if (all_name.contains(PMS_TOF_DELTA)) {
                    int16_t tof_delta = tofDelta(data_receive_->ori_dist, m_current_distance);
                    auto_show_frame.insert(PMS_TOF_DELTA, QString::number(tof_delta, 10));
                }

                //* cal data
                auto_show_frame.insert(PMS_OPTIMIZED_VALUE, getOptimizedValue());

                mst_used_data.distance.addData(data_receive_->corr_dist);
                mst_used_data.confidence.addData(data_receive_->confident);
            }
            complete_frame_parsed.unite(auto_show_frame);

            mm_comm_status[CSprm::sm_universal_tasks_name[EUniversalTasks::eTASK_GET_DATA].task_name].comm_status = IComm::ECommStatus::eCOMM_COMP;
        }
    }
    return true;
}

bool CSprm::interactionParsing(QByteArray &            str,
                               const int &             length,
                               QByteArray &            complete_frame,  // eCmdParseMode mode,
                               QMap<QString, QString> &complete_frame_parsed,
                               QMap<QString, QString> &auto_show_frame) {

    Q_UNUSED(length);
    QByteArray strSum = mst_protocol_comm.strPre + str;
    str.clear();


    if (!mst_protocol_comm.interaction_protocol_->interactionParing(strSum, complete_frame, complete_frame_parsed)) {
        mst_protocol_comm.strPre = strSum;
        return false;
    }

    // Common parsing logic
    mm_task_exec_info.clear();

    // 无法兼容，只能根据项目分别处理
    if ((mst_property.static_property_->device_name.model_name == "A1B") || (mst_property.static_property_->device_name.model_name == "A1C")) {
        //       IProtocol::StInteractionFrame* data_receive_ = mst_protocol_comm.analysis_protocol_->getInteractionFrame(complete_frame.data());

        CClient_besterP::StInteractionFrame *data_receive_ = reinterpret_cast<CClient_besterP::StInteractionFrame *>(complete_frame.data());

        //* 执行步骤ack output
        if (data_receive_->header == (uint16_t)CClient_besterP::EInteractionFrame::eHEADER) {  // bester 距离返回ACK协议不一样（无cmd id）->特殊处理
            QString convert_tmp = "";

            if (data_receive_->cmd == mst_comm_info_->cmd_id.find((uint16_t)EPmsAllId::eCALIB1)->mark) {
                complete_frame_parsed.insert(mi_spms_soc_->mm_calib_config[ISprmSoc::eCALIB1].calib_item_name, QString::number(data_receive_->cmd, 10));
                mm_task_exec_info = mi_spms_soc_->getCalib1Param(data_receive_->data_buff);
                complete_frame_parsed.unite(mm_task_exec_info);

                mst_comm_status_->comm_status              = eCOMM_COMP;
                mv_calib_task_list[eCALIB1_STEP].flag.stop = true;
            } else if (data_receive_->cmd == mst_comm_info_->cmd_id.find((uint16_t)EPmsAllId::eCALIB2)->mark) {
                complete_frame_parsed.insert(mi_spms_soc_->mm_calib_config[ISprmSoc::eCALIB2].calib_item_name, QString::number(data_receive_->cmd, 10));
                mm_task_exec_info = mi_spms_soc_->getCalib2Param(data_receive_->data_buff);
                complete_frame_parsed.unite(mm_task_exec_info);

                mst_comm_status_->comm_status              = eCOMM_COMP;
                mv_calib_task_list[eCALIB2_STEP].flag.stop = true;
            } else if (data_receive_->cmd == mst_comm_info_->cmd_id.find((uint16_t)EPmsAllId::ePROC_MODE)->mark) {
                complete_frame_parsed.insert("cmd:mode", QString::number(data_receive_->cmd, 10));
                complete_frame_parsed.insert("mode index:", QString::number(data_receive_->data_buff[0], 10));
            } else if (data_receive_->cmd == mst_comm_info_->cmd_id.find((uint16_t)EPmsAllId::eEXPAND_DIST)->mark) {
                complete_frame_parsed.insert("distance", QString::number(data_receive_->data_buff[0], 10));
                complete_frame_parsed.insert("signal", QString::number(data_receive_->data_buff[1], 10));
                complete_frame_parsed.insert("range", QString::number(data_receive_->data_buff[2], 10));
                complete_frame_parsed.insert("confidence", QString::number(data_receive_->data_buff[3], 10));
            } else if (data_receive_->cmd == mst_comm_info_->cmd_id.find((uint16_t)EPmsAllId::eVERSION)->mark) {
                complete_frame_parsed.insert("cmd:version", QString::number(data_receive_->cmd, 10));
                complete_frame_parsed.insert("project", CTypeConvert::changefromHex_to_ascii(data_receive_->data_buff, 8));
                //                complete_frame_parsed.insert("version", QString::number(data_receive_->data_buff[8], 16) + "." + QString::number(data_receive_->data_buff[9], 16) + "." + \
                  //                        QString::number(data_receive_->data_buff[10], 16) + "." + QString::number(data_receive_->data_buff[11], 16));
                complete_frame_parsed.insert("software version",
                                             QString::number(data_receive_->data_buff[8], 10) + "." + QString::number(data_receive_->data_buff[9], 10));
                complete_frame_parsed.insert("hardware version",
                                             QString::number(data_receive_->data_buff[10], 10) + "." + QString::number(data_receive_->data_buff[11], 10));
                complete_frame_parsed.insert("date",
                                             QString::number(data_receive_->data_buff[12], 10) + "." + QString::number(data_receive_->data_buff[13], 10) + "." +
                                                 QString::number(data_receive_->data_buff[14], 10));
            } else if (data_receive_->cmd == mst_comm_info_->cmd_id.find((uint16_t)EPmsAllId::ePARAM)->mark) {
                complete_frame_parsed.insert("cmd:param", QString::number(data_receive_->cmd, 10));
                complete_frame_parsed.insert("cross-talk:", QString(data_receive_->data_buff[0]));
                complete_frame_parsed.insert("ref offset:", QString::number(data_receive_->data_buff[2] | ((data_receive_->data_buff[3] << 8) & 0xff00), 10));
            } else if (data_receive_->cmd == mst_comm_info_->cmd_id.find((uint16_t)EPmsAllId::eCHIP_ID)->mark) {
                complete_frame_parsed.insert("cmd:id", QString::number(data_receive_->cmd, 10));
                for (uint8_t for_i = 0; for_i < 8; for_i++) {
                    convert_tmp += QString::number(data_receive_->data_buff[for_i], 16).toUpper();
                    if (for_i != 7)
                        convert_tmp += " ";
                }
                complete_frame_parsed.insert("chip id", convert_tmp);
            } else if (data_receive_->cmd == mst_comm_info_->cmd_id.find((uint16_t)EPmsAllId::eDDS_LOG)->mark) {
                complete_frame_parsed.insert("cmd:dds-log", QString::number(data_receive_->cmd, 10));
                complete_frame_parsed.insert(
                    "soc dds",
                    QString::number(data_receive_->data_buff[0], 10));  // data_receive_->data_buff[0] | ((data_receive_->data_buff[1] << 8) & 0xff00), 10));
                complete_frame_parsed.insert(
                    "mcu dds",
                    QString::number(data_receive_->data_buff[1], 10));  // data_receive_->data_buff[2] | ((data_receive_->data_buff[3] << 8) & 0xff00), 10));
            } else {
            }
        } else {
            complete_frame_parsed.insert("distance", QString::number(data_receive_->cmd, 10));
            complete_frame_parsed.insert("signal", QString::number(data_receive_->data_buff[0], 10));
            complete_frame_parsed.insert("range", QString::number(data_receive_->data_buff[1], 10));

            auto_show_frame.insert("distance", QString::number(data_receive_->cmd, 10));
            auto_show_frame.insert("backgroud", QString::number(data_receive_->cmd, 10));
            auto_show_frame.insert("signal", complete_frame_parsed["signal"]);


            mm_comm_status[CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_WORK].task_name].comm_status =
                IComm::ECommStatus::eCOMM_COMP;
        }
    } else if ((mst_property.static_property_->device_name.model_name == "A2") || (mst_property.static_property_->device_name.model_name == "A3")) {

        CModel_simpleXorP::StInteractionFrame *data_receive_ = reinterpret_cast<CModel_simpleXorP::StInteractionFrame *>(complete_frame.data());

        //* 执行步骤ack output
        //    if(data_receive_->header == (uint16_t)CModel_simpleXorP::EInteractionFrame::eHEADER) { //bester 距离返回ACK协议不一样（无cmd id）->特殊处理
        QString                convert_tmp = "";
        QMap<QString, QString> ack_callback;
        uint16_t               calib_status = 0, dist_tmp = 0;

        complete_frame_parsed.insert("id:" + QString(m_id_Enum.valueToKey((uint8_t)data_receive_->id)), QString::number(data_receive_->id, 10));

        if (data_receive_->id == mst_comm_info_->cmd_id.find((uint16_t)EPmsAllId::eCHIP_ID)->mark) {
            for (uint8_t for_i = 0; for_i < 8; for_i++) {
                convert_tmp += QString::number(data_receive_->data_buff[for_i], 16).toUpper();
                if (for_i != 7)
                    convert_tmp += " ";
            }
            mm_task_exec_info.insert(DEV_CHIP_ID, convert_tmp);
            complete_frame_parsed.unite(mm_task_exec_info);

            mm_comm_status[CSprm::sm_basic_tasks_name[ICommDevice::ECommDecBasicTasks::eTASK_CHIP_ID].task_name].comm_status = IComm::ECommStatus::eCOMM_COMP;
        } else if (data_receive_->id == mst_comm_info_->cmd_id.find((uint16_t)EPmsAllId::eVERSION)->mark) {
            mm_task_exec_info.insert(DEV_PROJECT, CTypeConvert::changefromHex_to_ascii(data_receive_->data_buff, 8));
            mm_task_exec_info.insert(DEV_SOFT_VERSION,
                                     QString::number(data_receive_->data_buff[8], 10) + "." + QString::number(data_receive_->data_buff[9], 10));
            mm_task_exec_info.insert(DEV_HARD_VERSION,
                                     QString::number(data_receive_->data_buff[10], 10) + "." + QString::number(data_receive_->data_buff[11], 10));
            mm_task_exec_info.insert(DEV_FIRMWARE_DATE,
                                     QString::number(data_receive_->data_buff[12], 10) + "." + QString::number(data_receive_->data_buff[13], 10) + "." +
                                         QString::number(data_receive_->data_buff[14], 10));
            complete_frame_parsed.unite(mm_task_exec_info);

            mm_comm_status[CSprm::sm_basic_tasks_name[ICommDevice::ECommDecBasicTasks::eTASK_VERSION].task_name].comm_status = IComm::ECommStatus::eCOMM_COMP;
        } else if (data_receive_->id == mst_comm_info_->cmd_id.find((uint16_t)EPmsAllId::ePROC_MODE)->mark) {
            uint16_t mode_tmp = 0;

            memcpy(&mode_tmp, data_receive_->data_buff, 2);
            LOG_INFO(MyLogger::LogType::COMM, QString("system mode value: %1").arg(mode_tmp));

            // 内容解析
            auto system_mode_list = mst_comm_info_->cmd_id[(uint16_t)EPmsAllId::ePROC_MODE].options;  //获取解析内容
            for (auto it = system_mode_list.begin(); it != system_mode_list.end(); it++) {
                if (mode_tmp == it.value()) {  //
                    // 内容显示
                    complete_frame_parsed.insert(QString("%1").arg(it.key()), QString::number(mode_tmp, 10));

                    // 任务处理
                    mm_comm_status[CSprm::sm_universal_tasks_name[EUniversalTasks::eTASK_SYSTEM_MODE].task_name].comm_status = IComm::ECommStatus::eCOMM_COMP;
                }
            }


            // switch ((EA2LidarMode)mode_tmp) {
            // case eTOF_CALIBRATION_MODE:
            //     complete_frame_parsed.insert("calib mode:", QString::number(mode_tmp, 10));
            //     mm_comm_status[CSprm::sm_universal_tasks_name[EUniversalTasks::eTASK_CALIB_MODE].task_name].comm_status = IComm::ECommStatus::eCOMM_COMP;
            //     break;

            // case eTOF_OPTIMIZATION_MODE:
            //     complete_frame_parsed.insert("analysis mode", QString::number(mode_tmp, 10));
            //     mm_comm_status[CSprm::sm_universal_tasks_name[EUniversalTasks::eTASK_ANALYSIS_MODE].task_name].comm_status = IComm::ECommStatus::eCOMM_COMP;
            //     break;

            // case eTOF_SINGLE_RANGE_MODE:
            //     complete_frame_parsed.insert("measure mode", QString::number(mode_tmp, 10));
            //     mm_comm_status[CSprm::sm_universal_tasks_name[EUniversalTasks::eTASK_VERIFY_MODE].task_name].comm_status = IComm::ECommStatus::eCOMM_COMP;
            //     break;

            // case eTOF_WORK_MODE:
            //     complete_frame_parsed.insert("work mode", QString::number(mode_tmp, 10));
            //     mm_comm_status[CSprm::sm_universal_tasks_name[EUniversalTasks::eTASK_WORK_MODE].task_name].comm_status = IComm::ECommStatus::eCOMM_COMP;
            //     break;

            // default:
            //     complete_frame_parsed.insert("mode:", QString::number(mode_tmp, 10));
            //     break;
            // }
        } else if (data_receive_->id == mst_comm_info_->cmd_id.find((uint16_t)EPmsAllId::eLED_IO)->mark) {
            complete_frame_parsed.insert("led:", QString(data_receive_->data_buff[0]));

        } else if (data_receive_->id == mst_comm_info_->cmd_id.find((uint16_t)EPmsAllId::eCALIB_ITEM)->mark) {
            mst_comm_status_->comm_status = eCOMM_COMP;
            //        memcpy(&calib_status, data_receive_->data_buff, 2); //底板无返回标识位

            switch (m_calib_mode) {  //
            case ECalibMode::eXTALK:
                complete_frame_parsed.insert(mi_spms_soc_->mm_calib_config[ISprmSoc::eCALIB1].calib_item_name, QString::number(data_receive_->id, 10));
                mm_task_exec_info                          = mi_spms_soc_->getCalib1Param(data_receive_->data_buff);
                mv_calib_task_list[eCALIB1_STEP].flag.stop = true;
                break;
            case ECalibMode::eOFFSET:
                complete_frame_parsed.insert(mi_spms_soc_->mm_calib_config[ISprmSoc::eCALIB2].calib_item_name, QString::number(data_receive_->id, 10));
                mm_task_exec_info                          = mi_spms_soc_->getCalib2Param(data_receive_->data_buff);
                mv_calib_task_list[eCALIB2_STEP].flag.stop = true;
                break;
            case ECalibMode::eREF_TOF:
                break;
            case ECalibMode::eCG_RADIO:
                break;
            case ECalibMode::eTEMP:
                break;
                //        case eACCURACY:
                //            break;
            default:
                break;
            }
            complete_frame_parsed.unite(mm_task_exec_info);
        } else if (data_receive_->id == mst_comm_info_->cmd_id.find((uint16_t)EPmsAllId::ePARAM)->mark) {
            complete_frame_parsed.insert("fre:", QString(data_receive_->data_buff[0]));

        } else if (data_receive_->id == mst_comm_info_->cmd_id.find((uint16_t)EPmsAllId::eQUERY_DIST)->mark) {
            memcpy(&dist_tmp, data_receive_->data_buff, 2);
            mm_task_exec_info.insert(PMS_DIST_MM, QString::number(dist_tmp, 10));
            auto_show_frame.insert(PMS_DIST_MM, mm_task_exec_info[PMS_DIST_MM]);
            complete_frame_parsed.unite(mm_task_exec_info);
            mst_used_data.distance.addData(dist_tmp);

            mm_comm_status[CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_GET_DATA].task_name].comm_status =
                IComm::ECommStatus::eCOMM_COMP;
        } else if (data_receive_->id == mst_comm_info_->cmd_id.find((uint16_t)EPmsAllId::eINTEGRAL_CNT)->mark) {
            complete_frame_parsed.insert("integral cnt:", QString(data_receive_->data_buff[0]));
        } else if (data_receive_->id == mst_comm_info_->cmd_id.find((uint16_t)EPmsAllId::eSAMPLE_VOL_VARS)->mark) {
            StBatVars *st_bat_vars_ = reinterpret_cast<StBatVars *>(data_receive_->data_buff);
            complete_frame_parsed.insert("adc data:", QString::number(st_bat_vars_->adc_data, 10));
            complete_frame_parsed.insert("vdd", QString::number(st_bat_vars_->actual_vdd, 10));
            complete_frame_parsed.insert("factor", QString::number(st_bat_vars_->sample_vol_correct_factor, 10));
            complete_frame_parsed.insert("sample vol", QString::number(st_bat_vars_->cur_sample_voltage, 10));
        } else if (data_receive_->id == mst_comm_info_->cmd_id.find((uint16_t)EPmsAllId::eDDS_LOG)->mark) {
            complete_frame_parsed.insert(DEV_DDS, QString::number(data_receive_->data_buff[0] | ((data_receive_->data_buff[1] << 8) & 0xff00), 10));
            // complete_frame_parsed.insert(DEV_DYM_DDS, QString::number(data_receive_->data_buff[2], 10));

            mm_task_exec_info.insert(DEV_DDS, complete_frame_parsed[DEV_DDS]);

            //            mm_comm_status[CSprm::sm_basic_tasks_name[ICommDevice::eREAD_DDS].task_name].comm_status = IComm::ECommStatus::eCOMM_COMP;
        } else {
        }
    } else {
    }

    //    // Get QMetaEnum for protocol IDs
    //    static QMetaEnum m_id_Enum = QMetaEnum::fromType<CSprm::EProtocolId>();

    //    //* 执行步骤ack output
    //    if (data_receive_->header == (uint16_t)CModel_simpleCheckSumP::EInteractionFrame::eHEADER) {
    //        QString                convert_tmp = "";
    //        QMap<QString, QString> ack_callback;
    //        uint16_t               mode_tmp = 0, dist_tmp = 0;

    //        //* 执行步骤ack output
    //        //        if(data_receive_->header == (uint16_t)CModel_simpleCheckSumP::EInteractionFrame::eHEADER) { //bester 距离返回ACK协议不一样（无cmd
    //        //        id）->特殊处理
    //        QString                convert_tmp = "";
    //        QMap<QString, QString> ack_callback;
    //        uint16_t               mode_tmp = 0, dist_tmp = 0;
    //        switch ((EProtocolId)data_receive_->id) {
    //            complete_frame_parsed.insert("id:" + QString(m_id_Enum.valueToKey((uint8_t)data_receive_->id)), QString::number(data_receive_->id, 10));
    //        case EProtocolId::eCHIP_ID:
    //            for (uint8_t for_i = 0; for_i < 8; for_i++) {
    //                convert_tmp += QString::number(data_receive_->data_buff[for_i], 16).toUpper();
    //                if (for_i != 7)
    //                    convert_tmp += " ";
    //            }
    //            mm_task_exec_info.insert(DEV_CHIP_ID, convert_tmp);
    //            complete_frame_parsed.unite(mm_task_exec_info);

    //            mm_comm_status[CSprm::sm_basic_tasks_name[ICommDevice::ECommDecBasicTasks::eTASK_CHIP_ID].task_name].comm_status =
    //            IComm::ECommStatus::eCOMM_COMP; break;

    //        case EProtocolId::eVERSION:
    //            mm_task_exec_info.insert(DEV_PROJECT, CTypeConvert::changefromHex_to_ascii(data_receive_->data_buff, 8));
    //            mm_task_exec_info.insert(DEV_SOFT_VERSION,
    //                                     QString::number(data_receive_->data_buff[3], 10) + "." + QString::number(data_receive_->data_buff[4], 10));
    //            mm_task_exec_info.insert(DEV_HARD_VERSION,
    //                                     QString::number(data_receive_->data_buff[5], 10) + "." + QString::number(data_receive_->data_buff[6], 10));
    //            //        mm_task_exec_info.insert(DEV_FIRMWARE_DATE, QString::number(data_receive_->data_buff[12], 10) + "." +
    //            //        QString::number(data_receive_->data_buff[13], 10) + "." + QString::number(data_receive_->data_buff[14], 10));
    //            complete_frame_parsed.unite(mm_task_exec_info);

    //            mm_comm_status[CSprm::sm_basic_tasks_name[ICommDevice::ECommDecBasicTasks::eTASK_VERSION].task_name].comm_status =
    //            IComm::ECommStatus::eCOMM_COMP; break;

    //        case EProtocolId::eCALIB1:
    //            complete_frame_parsed.insert(mi_spms_soc_->mm_calib_config[ISprmSoc::eCALIB1].calib_item_name, QString::number(data_receive_->id, 10));
    //            mm_task_exec_info = mi_spms_soc_->getCalib1Param(data_receive_->data_buff);
    //            complete_frame_parsed.unite(mm_task_exec_info);

    //            //* command ack
    //            mst_comm_status_->comm_status              = eCOMM_COMP;
    //            mv_calib_task_list[eCALIB1_STEP].flag.stop = true;
    //            break;

    //        case EProtocolId::eCALIB2:
    //            complete_frame_parsed.insert(mi_spms_soc_->mm_calib_config[ISprmSoc::eCALIB2].calib_item_name, QString::number(data_receive_->id, 10));
    //            mm_task_exec_info = mi_spms_soc_->getCalib2Param(data_receive_->data_buff);
    //            complete_frame_parsed.unite(mm_task_exec_info);

    //            mst_comm_status_->comm_status              = eCOMM_COMP;
    //            mv_calib_task_list[eCALIB2_STEP].flag.stop = true;
    //            break;

    //        case EProtocolId::eLED_IO:
    //            complete_frame_parsed.insert("LED index:", QString::number(data_receive_->data_buff[0], 10));
    //            break;

    //            //        case EProtocolId::eSET_RANGE:
    //            //            complete_frame_parsed.insert("cmd: set dist", QString::number(data_receive_->cmd, 10));
    //            //            complete_frame_parsed.insert("set dist index:", QString::number(data_receive_->data_buff[0], 10));
    //            //            break;

    //        case EProtocolId::eMODE_MODIFY:
    //            mode_tmp = data_receive_->data_buff[0];
    //            complete_frame_parsed.insert("mode index:", QString::number(mode_tmp, 10));
    //            switch ((EMode)mode_tmp) {
    //            case kCALIB_MODE:
    //                mm_comm_status[CSprm::sm_universal_tasks_name[ICommDevice::eCALIB_MODE].task_name].comm_status = IComm::ECommStatus::eCOMM_COMP;
    //                break;
    //            case kMEASURE_MODE:
    //                mm_comm_status[CSprm::sm_universal_tasks_name[ICommDevice::eTASK_VERIFY_MODE].task_name].comm_status = IComm::ECommStatus::eCOMM_COMP;
    //                break;
    //            case kWORK_MODE:
    //                mm_comm_status[CSprm::sm_universal_tasks_name[ICommDevice::eTASK_WORK_MODE].task_name].comm_status = IComm::ECommStatus::eCOMM_COMP;
    //                break;
    //            default:
    //                mm_comm_status[CSprm::sm_universal_tasks_name[ICommDevice::eTASK_VERIFY_MODE].task_name].comm_status = IComm::ECommStatus::eCOMM_COMP;
    //                break;
    //            }
    //            break;

    //            //    case EProtocolId::eDATA_MODE:
    //            //        mode_tmp = data_receive_->data_buff[0];
    //            //        complete_frame_parsed.insert("mode index:", QString::number(mode_tmp, 10));
    //            //        switch ((EDataOutputMode)mode_tmp) {
    //            //        case eNOT_OUTPUT_MODE:
    //            //            mm_comm_status[CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_OPTIMIZE_MODE].task_name].comm_status =
    //            //            IComm::ECommStatus::eCOMM_COMP; break;
    //            //        case eCURVE_SHOW_OUTPUT_MODE:
    //            //            mm_comm_status[CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_ANALYSIS_MODE].task_name].comm_status =
    //            //            IComm::ECommStatus::eCOMM_COMP; break;
    //            //        case eDISTANCE_OUTPUT_MODE:
    //            ////            mm_comm_status[CSprm::sm_universal_tasks_name[ICommDevice::eTASK_WORK_MODE].task_name].comm_status =
    //            IComm::ECommStatus::eCOMM_COMP;
    //            //            break;
    //            //        default:
    //            ////            mm_comm_status[CSprm::sm_universal_tasks_name[ICommDevice::eTASK_VERIFY_MODE].task_name].comm_status =
    //            /// IComm::ECommStatus::eCOMM_COMP;
    //            //            break;
    //            //        }
    //            //        break;

    //        case EProtocolId::eQUERY_DIST:
    //            //第一次从上次状态继续，导致数据还是旧数据
    //            if (data_receive_->data_buff[0] == 0xAA) {
    //                dist_tmp = data_receive_->data_buff[1] | ((data_receive_->data_buff[2] << 8) & 0xff00);

    //                mm_task_exec_info.insert(PMS_DIST_MM, QString::number(dist_tmp, 10));
    //                auto_show_frame.insert(PMS_DIST_MM, mm_task_exec_info[PMS_DIST_MM]);
    //                complete_frame_parsed.unite(mm_task_exec_info);
    //                mst_used_data.distance.addData(dist_tmp);
    //            } else {
    //                dist_tmp = data_receive_->data_buff[1];

    //                mm_task_exec_info.insert(PMS_WORK_STATUS, data_receive_->data_buff[0] == true ? "in" : "out");
    //                mm_task_exec_info.insert(PMS_DIST_CM, QString::number((uint8_t)dist_tmp, 10));
    //                mm_task_exec_info.insert(PMS_WORK_BACKGROUND, QString::number(data_receive_->data_buff[2], 10));

    //                auto_show_frame.insert(PMS_DIST_CM, mm_task_exec_info[PMS_DIST_CM]);
    //                complete_frame_parsed.unite(mm_task_exec_info);
    //                mst_used_data.distance.addData(dist_tmp);
    //            }
    //            mm_comm_status[CSprm::sm_universal_tasks_name[IProximitySensor::EUniversalTasks::eTASK_VERIFY].task_name].comm_status =
    //                IComm::ECommStatus::eCOMM_COMP;
    //            break;

    //        case EProtocolId::eRARAM_RW:
    //            mm_comm_status[CSprm::sm_universal_tasks_name[ICommDevice::eOPTIMIZE_PARAM_TASK].task_name].comm_status = IComm::ECommStatus::eCOMM_COMP;
    //            break;

    //        case EProtocolId::eDDS_LOG:
    //            complete_frame_parsed.insert(DEV_DDS, QString::number(data_receive_->data_buff[0] | ((data_receive_->data_buff[1] << 8) & 0xff00), 10));
    //            //        complete_frame_parsed.insert(DEV_DYM_DDS, QString::number(data_receive_->data_buff[2], 10));

    //            mm_task_exec_info.insert(DEV_DDS, complete_frame_parsed[DEV_DDS]);

    //            mm_comm_status[CSprm::sm_basic_tasks_name[ICommDevice::eREAD_DDS].task_name].comm_status = IComm::ECommStatus::eCOMM_COMP;

    //            break;

    //        default:  //
    //            complete_frame_parsed.insert("ack data:", QString::number(data_receive_->data_buff[0], 10));
    //            break;
    //        }
    //        //    }
    //        return true;

    //    };  //交互指令解析
}

// AUTO_REGISTER_DEVICE(Nova, A1, [](IPort *port, const QString &model) -> IProximitySensor * { return new CSprm(port, model); })
}  // namespace proximity_sensor
