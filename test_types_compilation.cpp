/**
 * @file test_types_compilation.cpp
 * @brief 简单测试文件验证类型定义重构是否正确
 */

// 测试Foundation层基础类型
#include "support/foundation/core/CommonTypes.h"

// 测试通信层扩展类型
#include "infrastructure/communication/include/LA/Communication/PortManagement/PortTypes.h"
#include "infrastructure/communication/include/LA/Communication/DataStructures/PortAttributes.h" 

// 测试适配器
#include "infrastructure/communication/include/LA/Communication/Adapters/PortInfoAdapters.h"

#include <iostream>

int main() {
    std::cout << "Testing type definitions after refactoring..." << std::endl;
    
    // 测试Foundation层基础类型
    LA::Foundation::Core::PortInfo basePort;
    basePort.portName = "COM1";
    basePort.portType = LA::Foundation::Core::PortType::Serial;
    basePort.status = LA::Foundation::Core::PortStatus::Available;
    
    // 测试通信层扩展类型
    LA::Communication::PortManagement::CommunicationPortInfo commPort(basePort);
    commPort.portId = "serial_001";
    commPort.displayName = "Serial Port 1";
    
    // 测试数据结构层扩展类型
    LA::Communication::DataStructures::SystemPortInfo systemPort(basePort);
    systemPort.systemPath = "/dev/ttyUSB0";
    systemPort.hardwareId = "USB\\VID_1234&PID_5678";
    
    // 测试适配器转换
    auto convertedBase = LA::Communication::Adapters::PortInfoAdapters::toBase(commPort);
    auto convertedComm = LA::Communication::Adapters::PortInfoAdapters::toComm(convertedBase);
    
    std::cout << "✅ Foundation基础类型定义正确" << std::endl;
    std::cout << "✅ Communication扩展类型定义正确" << std::endl; 
    std::cout << "✅ SystemPortInfo扩展类型定义正确" << std::endl;
    std::cout << "✅ 适配器转换功能正确" << std::endl;
    std::cout << "✅ 类型重构验证通过！" << std::endl;
    
    return 0;
}