# LA项目设计原则

## 核心设计原则

### 1. 单一职责和单一来源 (Single Responsibility & Single Source of Truth)
- 每个模块只负责一个功能，避免职责混杂
- 每类数据只在一个地方定义和维护
- 设备定义、协议规则、映射关系统一管理

### 2. 类型定义分层原则 (Type Definition Layering Principle)

**判断准则**：
- **跨模块共享且稳定** → 放入 `foundation/core/`
- **模块专用或易变** → 保留在各自模块内
- **频繁变化** → 通过适配器隔离

**Foundation 放置规则**：
```cpp
namespace LA::Foundation::Core {
    // ✅ 基础枚举和标识符
    enum class DeviceType { SPRM, Motor, Sensor };
    enum class ProtocolType { Modbus, Custom };
    enum class ConnectionType { Serial, TCP, UDP };
    
    // ✅ 核心数据结构（稳定且跨模块）
    struct DeviceId { QString value; };
    struct PortId { QString value; };
    
    // ❌ 不放入：具体实现、配置细节、内部状态
}
```

**模块内保留**：
```cpp
// 各模块保留自己的实现细节
namespace LA::Device::Internal {
    struct SprmConfig { /* 具体配置 */ };
}

namespace LA::Communication::Internal {
    struct ConnectionState { /* 连接状态 */ };
}
```

### 3. 组合优于继承 (Composition over Inheritance)
- 最大继承深度：3层
- 通过依赖注入实现功能复用
- 接口基于多个实现时才抽象

### 4. 接口与数据分离原则
- 接口只定义行为契约
- 数据结构专注信息表达
- 通过适配器连接不同层次

## 架构原则

### 1. 最小接口 (Minimal Interface)
接口只暴露必要功能，客户端不依赖不需要的方法。

### 2. 职责分离 (Separation of Concerns)
- **Connection**: 纯数据传输
- **Protocol**: 纯编解码
- **Device**: 业务逻辑封装
- **Registry**: 对象生命周期管理

### 3. 无状态设计 (Stateless Design)
组件尽量无状态，状态集中管理，提高可测试性。

### 4. 配置驱动 (Configuration Driven)
映射关系、行为规则通过配置定义，支持运行时调整。

## 实践检查

### 类型定义检查清单
- [ ] 基础枚举是否在 foundation 中？
- [ ] 跨模块数据结构是否统一定义？
- [ ] 实现细节是否保留在模块内？
- [ ] 是否存在重复类型定义？

### 代码质量检查
- [ ] 继承深度是否超过3层？
- [ ] 接口是否过于庞大？
- [ ] 模块职责是否清晰？
- [ ] 组件是否可独立测试？

---

*简洁明确的设计原则，专注解决实际问题。*