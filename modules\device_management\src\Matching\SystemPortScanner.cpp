#include "LA/DeviceManagement/Matching/SystemPortScanner.h"
#include <QDebug>
#include <QNetworkInterface>
#include <QSerialPort>


namespace LA {
namespace DeviceManagement {
namespace Matching {

// 使用Foundation层基础类型定义和转换函数
using LA::Foundation::Core::PortInfo;
using LA::Foundation::Core::PortStatus;
using LA::Foundation::Core::portStatusToString;
using LA::Foundation::Core::PortType;

QList<PortInfo> SystemPortScanner::scanAvailablePorts() {
    qDebug() << "Starting port scan...";

    QList<PortInfo> allPorts;

    // 扫描串口
    auto serialPorts = scanSerialPorts();
    allPorts.append(serialPorts);

    // 扫描网络端口
    auto networkPorts = scanNetworkPorts();
    allPorts.append(networkPorts);

    qDebug() << "Total ports found:" << allPorts.size();
    return allPorts;
}

PortInfo SystemPortScanner::getPortInfo(const QString &portName) {
    // 先检查是否是串口
    auto serialPorts = QSerialPortInfo::availablePorts();
    for (const auto &serialInfo : serialPorts) {
        if (serialInfo.portName() == portName) {
            return createPortInfoFromSerial(serialInfo);
        }
    }

    // 检查是否是网络端口格式 (IP:Port)
    if (portName.contains(":")) {
        auto parts = portName.split(":");
        if (parts.size() == 2) {
            bool ok;
            int  port = parts[1].toInt(&ok);
            if (ok) {
                return createPortInfoFromNetwork(parts[0], port);
            }
        }
    }

    // 未找到或格式不正确
    return PortInfo();
}

bool SystemPortScanner::isPortAvailable(const QString &portName) {
    auto portInfo = getPortInfo(portName);
    if (!portInfo.isValid()) {
        return false;
    }

    if (portInfo.portType == PortType::Serial) {
        return testSerialPortAvailability(portName);
    } else if (portInfo.portType == PortType::TCP) {
        auto parts = portName.split(":");
        if (parts.size() == 2) {
            bool ok;
            int  port = parts[1].toInt(&ok);
            return ok && testNetworkPortAvailability(parts[0], port);
        }
    }

    return false;
}

QList<PortInfo> SystemPortScanner::scanSerialPorts() {
    QList<PortInfo> ports;
    auto            serialPorts = QSerialPortInfo::availablePorts();

    qDebug() << "Scanning serial ports, found:" << serialPorts.size();

    for (const auto &serialInfo : serialPorts) {
        auto portInfo = createPortInfoFromSerial(serialInfo);
        if (portInfo.isValid()) {
            ports.append(portInfo);
            qDebug() << "Serial port:" << portInfo.portName << "Status:" << portStatusToString(portInfo.status);
        }
    }

    return ports;
}

QList<PortInfo> SystemPortScanner::scanNetworkPorts() {
    QList<PortInfo> ports;

    // 获取本机网络接口
    auto interfaces = QNetworkInterface::allInterfaces();

    for (const auto &interface : interfaces) {
        if (interface.flags().testFlag(QNetworkInterface::IsRunning) && !interface.flags().testFlag(QNetworkInterface::IsLoopBack)) {

            auto entries = interface.addressEntries();
            for (const auto &entry : entries) {
                QString ip = entry.ip().toString();
                if (!ip.isEmpty() && entry.ip().protocol() == QAbstractSocket::IPv4Protocol) {
                    // 常见的工业设备端口
                    QList<int> commonPorts = {502, 503, 23, 80, 8080, 10001, 10002};
                    for (int port : commonPorts) {
                        auto portInfo = createPortInfoFromNetwork(ip, port);
                        if (portInfo.isValid()) {
                            ports.append(portInfo);
                        }
                    }
                }
            }
        }
    }

    qDebug() << "Network ports found:" << ports.size();
    return ports;
}

PortInfo SystemPortScanner::createPortInfoFromSerial(const QSerialPortInfo &serialInfo) {
    PortInfo info;
    info.portName = serialInfo.portName();
    info.portType = PortType::Serial;
    info.status   = serialInfo.isBusy() ? PortStatus::Busy : PortStatus::Available;

    // 添加详细属性
    info.properties["description"]  = serialInfo.description();
    info.properties["manufacturer"] = serialInfo.manufacturer();
    info.properties["serialNumber"] = serialInfo.serialNumber();
    info.properties["vendorId"]     = serialInfo.vendorIdentifier();
    info.properties["productId"]    = serialInfo.productIdentifier();

    return info;
}

PortInfo SystemPortScanner::createPortInfoFromNetwork(const QString &address, int port) {
    PortInfo info;
    info.portName = QString("%1:%2").arg(address).arg(port);
    info.portType = PortType::TCP;
    info.status   = testNetworkPortAvailability(address, port) ? PortStatus::Available : PortStatus::Error;

    // 添加网络属性
    info.properties["address"]  = address;
    info.properties["port"]     = port;
    info.properties["protocol"] = "TCP";

    return info;
}

bool SystemPortScanner::testSerialPortAvailability(const QString &portName) {
    QSerialPort port(portName);
    bool        canOpen = port.open(QIODevice::ReadWrite);
    if (canOpen) {
        port.close();
    }
    return canOpen;
}

bool SystemPortScanner::testNetworkPortAvailability(const QString &address, int port) {
    QTcpSocket socket;
    socket.connectToHost(address, port);
    bool connected = socket.waitForConnected(1000);  // 1秒超时

    if (connected) {
        socket.disconnectFromHost();
        if (socket.state() != QAbstractSocket::UnconnectedState) {
            socket.waitForDisconnected(1000);
        }
    }

    return connected;
}

}  // namespace Matching
}  // namespace DeviceManagement
}  // namespace LA