#pragma once

/**
 * @file PortInfoAdapters.h
 * @brief 端口信息类型适配器 - 实现不同层次间的类型转换
 * 
 * 根据 Linus 原则：
 * "好的架构通过适配器隔离实现与契约，而不是让每个地方都知道所有细节"
 */

#include "support/foundation/core/CommonTypes.h"
#include "../PortManagement/PortTypes.h"
#include "../DataStructures/PortAttributes.h"

namespace LA {
namespace Communication {
namespace Adapters {

/**
 * @brief 端口信息适配器工具类
 */
class PortInfoAdapters {
public:
    // ============================================
    // Foundation层 <-> Communication层 转换
    // ============================================
    
    /**
     * @brief 从Foundation基础信息转换到通信层详细信息
     */
    static PortManagement::CommunicationPortInfo toComm(
        const Foundation::Core::PortInfo& base) {
        return PortManagement::CommunicationPortInfo(base);
    }
    
    /**
     * @brief 从通信层详细信息转换到Foundation基础信息
     */
    static Foundation::Core::PortInfo toBase(
        const PortManagement::CommunicationPortInfo& comm) {
        Foundation::Core::PortInfo base;
        base.portName = comm.portName;
        base.portType = comm.portType;
        base.status = comm.status;
        base.description = comm.description;
        base.properties = comm.properties;
        return base;
    }
    
    /**
     * @brief 从Foundation基础信息转换到系统层信息
     */
    static DataStructures::SystemPortInfo toSystem(
        const Foundation::Core::PortInfo& base) {
        return DataStructures::SystemPortInfo(base);
    }
    
    /**
     * @brief 从系统层信息转换到Foundation基础信息
     */
    static Foundation::Core::PortInfo toBase(
        const DataStructures::SystemPortInfo& system) {
        Foundation::Core::PortInfo base;
        base.portName = system.portName;
        base.portType = system.portType;
        base.status = system.status;
        base.description = system.description;
        base.properties = system.properties;
        return base;
    }
    
    // ============================================
    // 批量转换支持
    // ============================================
    
    /**
     * @brief 批量转换Foundation列表到通信层列表
     */
    static QList<PortManagement::CommunicationPortInfo> toCommList(
        const QList<Foundation::Core::PortInfo>& baseList) {
        QList<PortManagement::CommunicationPortInfo> result;
        result.reserve(baseList.size());
        for (const auto& base : baseList) {
            result.append(toComm(base));
        }
        return result;
    }
    
    /**
     * @brief 批量转换通信层列表到Foundation列表  
     */
    static QList<Foundation::Core::PortInfo> toBaseList(
        const QList<PortManagement::CommunicationPortInfo>& commList) {
        QList<Foundation::Core::PortInfo> result;
        result.reserve(commList.size());
        for (const auto& comm : commList) {
            result.append(toBase(comm));
        }
        return result;
    }
};

} // namespace Adapters
} // namespace Communication
} // namespace LA