#pragma once

#include "../Core/DeviceManagementTypes.h"  // 使用Foundation层类型
#include <QStringList>
#include <QVariantMap>


namespace LA {
namespace DeviceManagement {
namespace Matching {

// 使用Foundation层类型 - 遵循单一来源原则
using LA::Foundation::Core::PortInfo;

/**
 * @brief 端口扫描器接口 - 纯粹的端口发现
 *
 * Linus: "只做一件事：扫描系统可用端口"
 * ✅ 负责: 端口发现、端口信息获取
 * ❌ 不涉及: 设备探测、协议处理、匹配算法
 */
class IPortScanner {
  public:
    virtual ~IPortScanner() = default;

    /**
     * @brief 扫描所有可用端口
     * @return 端口列表
     */
    virtual QList<PortInfo> scanAvailablePorts() = 0;

    /**
     * @brief 获取指定端口的详细信息
     * @param portName 端口名称
     * @return 端口信息，如果端口不存在返回无效的PortInfo
     */
    virtual PortInfo getPortInfo(const QString &portName) = 0;

    /**
     * @brief 验证端口是否可用
     * @param portName 端口名称
     * @return 是否可用
     */
    virtual bool isPortAvailable(const QString &portName) = 0;
};

}  // namespace Matching
}  // namespace DeviceManagement
}  // namespace LA