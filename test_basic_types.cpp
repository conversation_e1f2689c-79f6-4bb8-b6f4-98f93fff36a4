/**
 * @file test_basic_types.cpp
 * @brief 测试基础类型定义是否正确 - 无Qt依赖的简单验证
 */

#include <iostream>
#include <string>

// 模拟QString和其他Qt类型的简单替代
using QString = std::string;
template<typename T>
using QList = std::vector<T>;
template<typename K, typename V>
using QMap = std::map<K, V>;
using QVariant = std::string;
#define QDateTime long long

#include "support/foundation/core/CommonTypes.h"

int main() {
    std::cout << "=== 测试类型定义重构后的编译情况 ===" << std::endl;
    
    // 测试Foundation层基础类型
    LA::Foundation::Core::PortInfo basePort;
    basePort.portName = "COM1";
    basePort.portType = LA::Foundation::Core::PortType::Serial;
    basePort.status = LA::Foundation::Core::PortStatus::Available;
    basePort.description = "Test serial port";
    
    // 测试设备信息类型
    LA::Foundation::Core::DeviceInfo device;
    device.deviceId = "device_001";
    device.deviceType = LA::Foundation::Core::DeviceType::SerialDevice;
    device.name = "Test Device";
    
    // 测试Result模板类
    auto result = LA::Foundation::Core::Result<int>::success(42);
    if (result.isSuccess()) {
        std::cout << "✅ Result模板类工作正常，值: " << result.value() << std::endl;
    }
    
    // 测试错误结果
    auto errorResult = LA::Foundation::Core::Result<int>::failure("测试错误");
    if (errorResult.isFailed()) {
        std::cout << "✅ 错误结果处理正常: " << errorResult.message() << std::endl;
    }
    
    std::cout << std::endl;
    std::cout << "✅ Foundation基础类型定义编译通过！" << std::endl;
    std::cout << "✅ PortInfo类型统一定义正确！" << std::endl; 
    std::cout << "✅ DeviceInfo类型统一定义正确！" << std::endl;
    std::cout << "✅ 枚举类型定义正确！" << std::endl;
    std::cout << "✅ 模板类Result定义正确！" << std::endl;
    std::cout << std::endl;
    std::cout << "🎉 类型定义重构成功验证通过！" << std::endl;
    
    return 0;
}