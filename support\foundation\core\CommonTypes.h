#pragma once

#include <QByteArray>
#include <QMap>
#include <QString>
#include <QVariant>
#include <QVector>
#include <functional>
#include <memory>

namespace LA {
namespace Foundation {
namespace Core {

// ===============================================
// Foundation Layer: Pure Abstract Type Definitions
// Only defines interfaces, no business logic
// Serves as contracts between modules
// Never depends on upper layer business modules
// ===============================================

/**
 * @brief Common result code enumeration
 */
enum class ResultCode {
    Success              = 0,   // Success
    Failed               = 1,   // Failed
    Timeout              = 2,   // Timeout
    InvalidParameter     = 3,   // Invalid parameter
    NotSupported         = 4,   // Not supported
    NotConnected         = 5,   // Not connected
    Busy                 = 6,   // Busy
    NotInitialized       = 7,   // Not initialized
    PermissionDenied     = 8,   // Permission denied
    ResourceNotAvailable = 9,   // Resource not available
    UnknownError         = 999  // Unknown error
};

/**
 * @brief Operation result template class
 */
template <typename T> class Result {
  public:
    Result() : code_(ResultCode::Failed) {
    }
    Result(ResultCode code) : code_(code) {
    }
    Result(const T &value) : code_(ResultCode::Success), value_(value) {
    }
    Result(T &&value) : code_(ResultCode::Success), value_(std::move(value)) {
    }
    Result(ResultCode code, const QString &message) : code_(code), message_(message) {
    }

    bool isSuccess() const {
        return code_ == ResultCode::Success;
    }
    bool isFailed() const {
        return code_ != ResultCode::Success;
    }

    ResultCode code() const {
        return code_;
    }
    QString message() const {
        return message_;
    }

    const T &value() const {
        return value_;
    }
    T &value() {
        return value_;
    }

    void setCode(ResultCode code) {
        code_ = code;
    }
    void setMessage(const QString &message) {
        message_ = message;
    }
    void setValue(const T &value) {
        value_ = value;
        code_  = ResultCode::Success;
    }

    // Static factory methods
    static Result<T> success(const T &value) {
        return Result<T>(value);
    }

    static Result<T> failure(const QString &message, ResultCode code = ResultCode::Failed) {
        return Result<T>(code, message);
    }

    static Result<T> error(const QString &message, ResultCode code = ResultCode::Failed) {
        return Result<T>(code, message);
    }

  private:
    ResultCode code_;
    QString    message_;
    T          value_;
};

// ===============================================
// Basic Type Aliases
// ===============================================

/**
 * @brief Configuration parameters type
 */
using ConfigParameters = QMap<QString, QVariant>;

/**
 * @brief Generic property mapping type
 */
using PropertyMap = QMap<QString, QVariant>;

/**
 * @brief Generic status information
 */
struct StatusInfo {
    QString    name;         // Status name
    QVariant   value;        // Status value
    QString    unit;         // Unit
    QString    description;  // Description
    qint64     timestamp;    // Timestamp

    StatusInfo() : name(""), value(QVariant()), unit(""), description(""), timestamp(0) {
    }
    StatusInfo(const QString &n, const QVariant &v, const QString &u = "", const QString &desc = "", qint64 ts = 0)
        : name(n), value(v), unit(u), description(desc), timestamp(ts) {
    }
};

/**
 * @brief Status information list
 */
using StatusInfoList = QVector<StatusInfo>;

/**
 * @brief Generic event information
 */
struct EventInfo {
    QString    eventType;    // Event type
    QString    source;       // Event source
    QVariant   data;         // Event data
    qint64     timestamp;    // Timestamp
    QString    description;  // Description

    EventInfo() : eventType(""), source(""), data(QVariant()), timestamp(0), description("") {
    }
    EventInfo(const QString &type, const QString &src, const QVariant &d = QVariant(), qint64 ts = 0, const QString &desc = "")
        : eventType(type), source(src), data(d), timestamp(ts), description(desc) {
    }
};

/**
 * @brief Generic error information
 */
struct ErrorInfo {
    ResultCode code;       // Error code
    QString    message;    // Error message
    QString    source;     // Error source
    qint64     timestamp;  // Timestamp
    QVariant   context;    // Context information

    ErrorInfo() : code(ResultCode::UnknownError), message(""), source(""), timestamp(0), context(QVariant()) {
    }
    ErrorInfo(ResultCode c, const QString &msg, const QString &src = "", qint64 ts = 0, const QVariant &ctx = QVariant())
        : code(c), message(msg), source(src), timestamp(ts), context(ctx) {
    }
};

/**
 * @brief Version information
 */
struct VersionInfo {
    int     major;  // Major version
    int     minor;  // Minor version
    int     patch;  // Patch version
    QString build;  // Build information

    VersionInfo() : major(0), minor(0), patch(0), build("") {
    }
    VersionInfo(int maj, int min, int pat, const QString &b = "") : major(maj), minor(min), patch(pat), build(b) {
    }

    QString toString() const {
        QString version = QString("%1.%2.%3").arg(major).arg(minor).arg(patch);
        if (!build.isEmpty()) {
            version += QString("-%1").arg(build);
        }
        return version;
    }

    bool operator==(const VersionInfo &other) const {
        return major == other.major && minor == other.minor && patch == other.patch && build == other.build;
    }

    bool operator<(const VersionInfo &other) const {
        if (major != other.major)
            return major < other.major;
        if (minor != other.minor)
            return minor < other.minor;
        if (patch != other.patch)
            return patch < other.patch;
        return build < other.build;
    }
};

// ===============================================
// Device Related Types
// ===============================================

/**
 * @brief Device type enumeration
 */
enum class DeviceType {
    Unknown = 0,
    YJSensor = 1,
    ModbusDevice = 2,
    SerialDevice = 3,
    NetworkDevice = 4,
    CustomDevice = 100
};

/**
 * @brief Device information structure
 */
struct DeviceInfo {
    QString     deviceId;       // Device ID
    DeviceType  deviceType;     // Device type
    QString     name;           // Device name
    QString     description;    // Device description
    QString     manufacturer;   // Manufacturer
    QString     model;          // Model
    VersionInfo version;        // Version information
    QStringList capabilities;   // Device capabilities
    PropertyMap properties;     // Additional properties

    DeviceInfo() : deviceId(""), deviceType(DeviceType::Unknown), name(""), description(""), 
                   manufacturer(""), model(""), version(), capabilities(), properties() {
    }
    
    /**
     * @brief Check if device information is valid
     */
    bool isValid() const {
        return !deviceId.isEmpty() && deviceType != DeviceType::Unknown && !name.isEmpty();
    }
    
    /**
     * @brief Equality comparison operator
     */
    bool operator==(const DeviceInfo& other) const {
        return deviceId == other.deviceId && 
               deviceType == other.deviceType &&
               name == other.name;
    }
    
    /**
     * @brief Inequality comparison operator
     */
    bool operator!=(const DeviceInfo& other) const {
        return !(*this == other);
    }
};

/**
 * @brief Port type enumeration
 */
enum class PortType {
    Unknown = 0,
    Serial = 1,
    TCP = 2,
    UDP = 3,
    CAN = 4,
    USB = 5,
    Network = 6,
    Bluetooth = 7,
    Virtual = 8
};

/**
 * @brief Port status enumeration
 */
enum class PortStatus {
    Unknown = 0,
    Available = 1,
    InUse = 2,
    Error = 3,
    Disconnected = 4,
    Busy = 5,
    Connected = 6,
    Removed = 7
};

/**
 * @brief Port information structure
 */
struct PortInfo {
    QString    portName;     // Port name
    PortType   portType;     // Port type
    PortStatus status;       // Port status
    QString    description;  // Port description
    PropertyMap properties; // Additional properties

    PortInfo() : portName(""), portType(PortType::Unknown), status(PortStatus::Unknown), 
                 description(""), properties() {
    }
    
    /**
     * @brief Check if port information is valid
     */
    bool isValid() const {
        return !portName.isEmpty() && portType != PortType::Unknown;
    }
    
    /**
     * @brief Equality comparison operator
     */
    bool operator==(const PortInfo& other) const {
        return portName == other.portName && 
               portType == other.portType &&
               status == other.status;
    }
    
    /**
     * @brief Inequality comparison operator
     */
    bool operator!=(const PortInfo& other) const {
        return !(*this == other);
    }
};

// =======================================================
// Foundation Layer: Additional Type Aliases and Structs
// =======================================================

/**
 * @brief Connection configuration structure
 */
struct ConnectionConfig {
    QString name;           // Connection name
    QString portName;       // Port name
    PortType portType;      // Port type
    PropertyMap parameters; // Connection parameters
    
    ConnectionConfig() : name(""), portName(""), portType(PortType::Unknown), parameters() {}
};

/**
 * @brief Connection status enumeration
 */
enum class ConnectionStatus {
    Disconnected = 0,
    Connecting = 1,
    Connected = 2,
    Error = 3
};

/**
 * @brief Basic statistics structure
 */
struct DeviceStatistics {
    quint64 bytesReceived = 0;
    quint64 bytesSent = 0;
    quint64 packetsReceived = 0;
    quint64 packetsSent = 0;
    quint64 errorsCount = 0;
    qint64 lastActivityTime = 0;
    qint64 lastActivity = 0;    // Alternative name for compatibility
};

/**
 * @brief Port statistics structure
 */
using PortStatistics = DeviceStatistics;

// =======================================================
// Foundation Layer: Common Type Aliases
// =======================================================

/**
 * @brief Port information list
 */
using PortInfoList = QVector<PortInfo>;

/**
 * @brief Simple result for operations that don't return data
 */
using SimpleResult = Result<bool>;

// =======================================================
// Foundation Layer: Utility Functions
// =======================================================

/**
 * @brief Convert port type to string
 */
inline QString portTypeToString(PortType type) {
    switch (type) {
    case PortType::Serial: return "Serial";
    case PortType::TCP: return "TCP";
    case PortType::UDP: return "UDP";
    case PortType::CAN: return "CAN";
    case PortType::USB: return "USB";
    case PortType::Network: return "Network";
    case PortType::Bluetooth: return "Bluetooth";
    case PortType::Virtual: return "Virtual";
    case PortType::Unknown:
    default: return "Unknown";
    }
}

/**
 * @brief Convert port status to string
 */
inline QString portStatusToString(PortStatus status) {
    switch (status) {
    case PortStatus::Available: return "Available";
    case PortStatus::InUse: return "InUse";
    case PortStatus::Error: return "Error";
    case PortStatus::Disconnected: return "Disconnected";
    case PortStatus::Busy: return "Busy";
    case PortStatus::Connected: return "Connected";
    case PortStatus::Removed: return "Removed";
    case PortStatus::Unknown:
    default: return "Unknown";
    }
}

/**
 * @brief Convert device type to string
 */
inline QString deviceTypeToString(DeviceType type) {
    switch (type) {
    case DeviceType::YJSensor: return "YJSensor";
    case DeviceType::ModbusDevice: return "ModbusDevice";
    case DeviceType::SerialDevice: return "SerialDevice";
    case DeviceType::NetworkDevice: return "NetworkDevice";
    case DeviceType::CustomDevice: return "CustomDevice";
    case DeviceType::Unknown:
    default: return "Unknown";
    }
}

} // namespace Core
} // namespace Foundation
} // namespace LA
