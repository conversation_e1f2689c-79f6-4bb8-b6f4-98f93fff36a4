# Device Management Module
cmake_minimum_required(VERSION 3.16)
project(LA_DeviceManagement VERSION 1.0.0 LANGUAGES CXX)

# C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Qt配置
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

# 查找Qt依赖
find_package(Qt5 REQUIRED COMPONENTS Core SerialPort Network Concurrent)

# 源文件 - 简化版本（只编译核心文件）
set(DEVICE_MANAGEMENT_SOURCES
    # 核心管理器 - 启用基础实现
    src/DeviceManagementOrchestrator.cpp
    src/DeviceInstanceManager.cpp

    # 发现服务 - 启用基础实现
    src/Discovery/DeviceDiscoveryService.cpp

    # 匹配系统 - 部分编译
    # src/Matching/MatchingCoordinator.cpp      # 暂时禁用
    src/Matching/SystemPortScanner.cpp
    src/Matching/StandardDeviceProber.cpp
    # src/Matching/UniversalDeviceIdentifier.cpp # 暂时禁用
    src/Matching/BestMatchAlgorithm.cpp
)

# 头文件 - 简化版本
set(DEVICE_MANAGEMENT_HEADERS
    # 核心类型定义
    include/LA/DeviceManagement/Core/DeviceManagementTypes.h

    # 核心接口
    include/LA/DeviceManagement/Matching/IPortScanner.h
    include/LA/DeviceManagement/Matching/IDeviceProber.h
    include/LA/DeviceManagement/Matching/IDeviceIdentifier.h
    include/LA/DeviceManagement/Matching/IMatchingAlgorithm.h
    
    # 实现类
    include/LA/DeviceManagement/Matching/SystemPortScanner.h
    include/LA/DeviceManagement/Matching/StandardDeviceProber.h
    include/LA/DeviceManagement/Matching/BestMatchAlgorithm.h
    
    # 启用核心管理器头文件
    include/LA/DeviceManagement/DeviceManagementOrchestrator.h
    include/LA/DeviceManagement/DeviceInstanceManager.h
    include/LA/DeviceManagement/Discovery/DeviceDiscoveryService.h
    # include/LA/DeviceManagement/Matching/MatchingCoordinator.h
    # include/LA/DeviceManagement/Matching/UniversalDeviceIdentifier.h
)

# 创建库目标
add_library(LA_device_management_lib SHARED
    ${DEVICE_MANAGEMENT_SOURCES}
    ${DEVICE_MANAGEMENT_HEADERS}
)

# Foundation库已在上层构建中添加，无需重复添加

# 链接依赖 - Linus: "设备管理模块作为最上层，依赖下层模块"
target_link_libraries(LA_device_management_lib
    PUBLIC
        Qt5::Core
        Qt5::SerialPort
        Qt5::Network
        Qt5::Concurrent
    PRIVATE
        # === Foundation层依赖：提供统一基础类型定义 ===
        LA::Foundation  # Foundation层基础类型定义
        # === Linus原则：设备管理模块依赖基础设施层 ===
        LA_communication_lib  # 通讯基础设施依赖
        LA_Device_Core        # 设备核心模块依赖
        # LA::Foundation        # Foundation层基础类型定义 - 暂时禁用，使用头文件包含
)

# 设置包含目录
target_include_directories(LA_device_management_lib
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../device/include>  # Device模块头文件
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../../infrastructure/communication/include>  # Communication模块头文件
        $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/support/foundation>  # Foundation层基础类型
        $<INSTALL_INTERFACE:include>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/src
)

# 设置库属性
set_target_properties(LA_device_management_lib PROPERTIES
    OUTPUT_NAME "LA_device_management"
    VERSION ${PROJECT_VERSION}
    SOVERSION 1
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
    AUTOMOC ON
    AUTOUIC ON
    AUTORCC ON
)

# 注册为LA共享库
if(COMMAND register_la_shared_library)
    register_la_shared_library(LA_device_management_lib)
endif()

# 输出配置信息
message(STATUS "LA Device Management Module Configuration:")
message(STATUS "  Library: LA_device_management_lib")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Sources: ${DEVICE_MANAGEMENT_SOURCES}")
message(STATUS "  Headers: ${DEVICE_MANAGEMENT_HEADERS}")
message(STATUS "  Include Directory: ${CMAKE_CURRENT_SOURCE_DIR}/include")
message(STATUS "  Features:")
message(STATUS "    - Linus-style Modular Architecture")
message(STATUS "    - Single Responsibility Principle")
message(STATUS "    - Device-Port Auto Matching System")
message(STATUS "    - Minimal Module Design")
message(STATUS "    - Dependency Injection Pattern")
message(STATUS "    - 100% Testable Components")
message(STATUS "  Matching System Components:")
message(STATUS "    - IPortScanner: System port discovery")
message(STATUS "    - IDeviceProber: Device probing and communication")  
message(STATUS "    - IDeviceIdentifier: Device type identification")
message(STATUS "    - IMatchingAlgorithm: Optimal matching calculation")
message(STATUS "    - MatchingCoordinator: Component composition and orchestration")