/**
 * @file test_sprm_command_generation.cpp
 * @brief 测试优化后的SPRM指令生成功能
 * 
 * 验证三层优先级指令生成：
 * 1. XML标准数据（优先级1）
 * 2. 协议接口生成（优先级2）
 * 3. 算法生成（优先级3，后备）
 */

#include <QCoreApplication>
#include <QDebug>
#include <QVariantMap>
#include "modules/device/src/Capabilities/command/sprm/SprmCommandProvider.h"

using namespace LA::Device::Command;

void testNovaA1CommandGeneration()
{
    qDebug() << "\n=== 测试Nova-A1指令生成 ===";
    
    SprmCommandProvider provider("Nova-A1");
    
    // 配置Nova-A1设备
    QVariantMap config;
    config["model"] = "Nova-A1";
    config["protocol"] = "MODEL_SIMPLE_CHECKSUM_P";
    config["timeout"] = 5000;
    config["commands"] = QStringList{"CALIB1", "CALIB2", "LED_IO", "QUERY_DIST", "VERSION"};
    
    QVariantMap commandCodes;
    commandCodes["CALIB1"] = 1;
    commandCodes["CALIB2"] = 2;
    commandCodes["LED_IO"] = 6;
    commandCodes["QUERY_DIST"] = 10;
    commandCodes["VERSION"] = 11;
    config["command_codes"] = commandCodes;
    
    provider.loadConfiguration(config);
    
    // 测试指令生成
    QStringList testCommands = {"CALIB1", "CALIB2", "LED_IO", "QUERY_DIST", "VERSION"};
    
    for (const QString& commandId : testCommands) {
        QByteArray command = provider.generateCommand(commandId);
        QString hexString = SprmCommandProvider::toHexString(command);
        
        qDebug() << QString("指令 %1: %2").arg(commandId, -12).arg(hexString);
        
        // 验证预期结果（基于XML标准数据）
        QString expected;
        if (commandId == "CALIB1") expected = "55 5A 85 01 00 86";
        else if (commandId == "CALIB2") expected = "55 5A 85 02 00 87";
        else if (commandId == "LED_IO") expected = "55 5A 85 06 01 00 8C";
        else if (commandId == "QUERY_DIST") expected = "55 5A 85 0A 01 00 90";
        else if (commandId == "VERSION") expected = "55 5A 85 0B 00 90";
        
        if (hexString == expected) {
            qDebug() << QString("✅ %1 指令生成正确").arg(commandId);
        } else {
            qDebug() << QString("❌ %1 指令生成错误，期望: %2").arg(commandId, expected);
        }
    }
}

void testNovaA1BCommandGeneration()
{
    qDebug() << "\n=== 测试Nova-A1B (Bester协议) 指令生成 ===";
    
    SprmCommandProvider provider("Nova-A1B");
    
    // 配置Nova-A1B设备
    QVariantMap config;
    config["model"] = "Nova-A1B";
    config["protocol"] = "SPRM_BESTER_P";
    config["timeout"] = 5000;
    config["commands"] = QStringList{"CALIB1", "CALIB2", "LED_IO", "EXPAND_DIST", "VERSION"};
    
    QVariantMap commandCodes;
    commandCodes["CALIB1"] = 1;
    commandCodes["CALIB2"] = 2;
    commandCodes["LED_IO"] = 6;
    commandCodes["EXPAND_DIST"] = 10;
    commandCodes["VERSION"] = 11;
    config["command_codes"] = commandCodes;
    
    provider.loadConfiguration(config);
    
    // 测试指令生成
    QStringList testCommands = {"CALIB1", "CALIB2", "LED_IO", "EXPAND_DIST", "VERSION"};
    
    for (const QString& commandId : testCommands) {
        QByteArray command = provider.generateCommand(commandId);
        QString hexString = SprmCommandProvider::toHexString(command);
        
        qDebug() << QString("指令 %1: %2").arg(commandId, -12).arg(hexString);
        
        // 验证预期结果（基于XML标准数据）
        QString expected;
        if (commandId == "CALIB1") expected = "55 AA 04 01 FA";
        else if (commandId == "CALIB2") expected = "55 AA 04 02 FB";
        else if (commandId == "LED_IO") expected = "55 AA 05 06 01 00";
        else if (commandId == "EXPAND_DIST") expected = "55 AA 04 0A 03";
        else if (commandId == "VERSION") expected = "55 AA 04 0B 04";
        
        if (hexString == expected) {
            qDebug() << QString("✅ %1 指令生成正确").arg(commandId);
        } else {
            qDebug() << QString("❌ %1 指令生成错误，期望: %2").arg(commandId, expected);
        }
    }
}

void testNovaA2CommandGeneration()
{
    qDebug() << "\n=== 测试Nova-A2 (XOR协议) 指令生成 ===";
    
    SprmCommandProvider provider("Nova-A2");
    
    // 配置Nova-A2设备
    QVariantMap config;
    config["model"] = "Nova-A2";
    config["protocol"] = "SPRM_SIMPLE_XOR_P";
    config["timeout"] = 3000;
    config["commands"] = QStringList{"CALIB1", "CALIB2", "CALIB3", "CALIB4", "LED_IO", "QUERY_DIST"};
    
    QVariantMap commandCodes;
    commandCodes["CALIB1"] = 1;
    commandCodes["CALIB2"] = 2;
    commandCodes["CALIB3"] = 3;
    commandCodes["CALIB4"] = 4;
    commandCodes["LED_IO"] = 6;
    commandCodes["QUERY_DIST"] = 10;
    config["command_codes"] = commandCodes;
    
    provider.loadConfiguration(config);
    
    // 测试指令生成
    QStringList testCommands = {"CALIB1", "CALIB2", "CALIB3", "CALIB4", "LED_IO", "QUERY_DIST"};
    
    for (const QString& commandId : testCommands) {
        QByteArray command = provider.generateCommand(commandId);
        QString hexString = SprmCommandProvider::toHexString(command);
        
        qDebug() << QString("指令 %1: %2").arg(commandId, -12).arg(hexString);
        
        // 验证预期结果（基于XML标准数据）
        QString expected;
        if (commandId == "CALIB1") expected = "A5 F1 01 00 55";
        else if (commandId == "CALIB2") expected = "A5 F1 02 00 56";
        else if (commandId == "CALIB3") expected = "A5 F1 03 00 57";
        else if (commandId == "CALIB4") expected = "A5 F1 04 00 50";
        else if (commandId == "LED_IO") expected = "A5 F1 06 01 00 53";
        else if (commandId == "QUERY_DIST") expected = "A5 F1 0A 00 5F";
        
        if (hexString == expected) {
            qDebug() << QString("✅ %1 指令生成正确").arg(commandId);
        } else {
            qDebug() << QString("❌ %1 指令生成错误，期望: %2").arg(commandId, expected);
        }
    }
}

void testProviderInfo()
{
    qDebug() << "\n=== 测试提供者信息 ===";
    
    SprmCommandProvider provider("Nova-A1");
    QVariantMap info = provider.getProviderInfo();
    
    qDebug() << "提供者类型:" << info["provider_type"].toString();
    qDebug() << "当前型号:" << info["current_model"].toString();
    qDebug() << "支持的型号:" << info["supported_models"].toStringList();
    qDebug() << "支持的指令:" << info["supported_commands"].toStringList();
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "🚀 SPRM指令生成优化测试";
    qDebug() << "基于Linus原则的三层优先级架构";
    qDebug() << "1. XML标准数据（优先级1）";
    qDebug() << "2. 协议接口生成（优先级2）";
    qDebug() << "3. 算法生成（优先级3，后备）";
    
    testNovaA1CommandGeneration();
    testNovaA1BCommandGeneration();
    testNovaA2CommandGeneration();
    testProviderInfo();
    
    qDebug() << "\n🎯 测试完成！";
    
    return 0;
}
