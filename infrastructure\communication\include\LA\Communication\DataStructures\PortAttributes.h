#pragma once

/**
 * @file PortAttributes.h
 * @brief 端口属性结构体定义
 *
 * 定义端口相关的所有属性和状态信息
 */

// === Linus原则：引用Foundation层统一定义，避免重复 ===
#include "support/foundation/core/CommonTypes.h"

#include <QDateTime>
#include <QString>
#include <QStringList>
#include <QVariantMap>

namespace LA {
namespace Communication {
namespace DataStructures {

// 使用Foundation层的统一类型定义
using PortType = ::LA::Foundation::Core::PortType;

/**
 * @brief 端口状态枚举
 */
enum class PortState {
    Unavailable,  // 不可用
    Available,    // 可用
    Opening,      // 打开中
    Open,         // 已打开
    Closing,      // 关闭中
    Closed,       // 已关闭
    Error         // 错误状态
};

/**
 * @brief 连接状态枚举
 */
enum class ConnectionState {
    Disconnected,   // 未连接
    Connecting,     // 连接中
    Connected,      // 已连接
    Reconnecting,   // 重连中
    Disconnecting,  // 断开连接中
    Error           // 错误状态
};

/**
 * @brief 端口统计信息
 */
struct PortStatistics {
    // 基础计数
    quint64 totalBytesRead    = 0;  // 总读取字节数
    quint64 totalBytesWritten = 0;  // 总写入字节数
    quint64 totalReads        = 0;  // 总读取次数
    quint64 totalWrites       = 0;  // 总写入次数

    // 错误计数
    quint64 readErrors       = 0;  // 读取错误次数
    quint64 writeErrors      = 0;  // 写入错误次数
    quint64 connectionErrors = 0;  // 连接错误次数
    quint64 timeoutErrors    = 0;  // 超时错误次数

    // 性能统计
    double averageReadSpeed  = 0.0;  // 平均读取速度 (bytes/s)
    double averageWriteSpeed = 0.0;  // 平均写入速度 (bytes/s)
    double maxReadSpeed      = 0.0;  // 最大读取速度
    double maxWriteSpeed     = 0.0;  // 最大写入速度

    // 时间统计
    QDateTime firstAccess;             // 首次访问时间
    QDateTime lastAccess;              // 最后访问时间
    QDateTime statisticsStartTime;     // 统计开始时间
    qint64    totalConnectedTime = 0;  // 总连接时间(ms)

    /**
     * @brief 重置统计信息
     */
    void reset() {
        *this               = PortStatistics();
        statisticsStartTime = QDateTime::currentDateTime();
    }

    /**
     * @brief 计算成功率
     */
    double getSuccessRate() const {
        quint64 totalOperations = totalReads + totalWrites;
        if (totalOperations == 0)
            return 0.0;

        quint64 totalErrors = readErrors + writeErrors + connectionErrors + timeoutErrors;
        return (double)(totalOperations - totalErrors) / totalOperations * 100.0;
    }

    /**
     * @brief 获取运行时长(秒)
     */
    qint64 getUpTime() const {
        if (!statisticsStartTime.isValid())
            return 0;
        return statisticsStartTime.secsTo(QDateTime::currentDateTime());
    }
};

/**
 * @brief 端口能力描述
 */
struct PortCapabilities {
    bool supportsRead      = true;   // 支持读操作
    bool supportsWrite     = true;   // 支持写操作
    bool supportsAsync     = true;   // 支持异步操作
    bool supportsBroadcast = false;  // 支持广播
    bool supportsMulticast = false;  // 支持组播

    int maxConnections = 1;      // 最大连接数
    int maxDataSize    = 65536;  // 最大数据包大小
    int minDataSize    = 1;      // 最小数据包大小

    QStringList supportedBaudRates;  // 支持的波特率(串口)
    QStringList supportedDataBits;   // 支持的数据位(串口)
    QStringList supportedStopBits;   // 支持的停止位(串口)
    QStringList supportedParity;     // 支持的校验位(串口)
};

// 使用Foundation层的PortInfo作为基础
using BasePortInfo = ::LA::Foundation::Core::PortInfo;

/**
 * @brief 端口系统信息 - 扩展Foundation层基础信息，专注系统层属性
 */
struct SystemPortInfo : BasePortInfo {
    QString portId;       // 端口唯一标识
    QString displayName;  // 显示名称

    // 系统信息
    QString systemPath;  // 系统路径
    QString driverName;  // 驱动名称
    QString hardwareId;  // 硬件ID
    QString vendorId;    // 供应商ID
    QString productId;   // 产品ID

    // 位置信息
    QString location;  // 物理位置
    QString busInfo;   // 总线信息

    SystemPortInfo() : BasePortInfo() {
    }
    
    // 从基础信息构造
    explicit SystemPortInfo(const BasePortInfo& base) : BasePortInfo(base) {
    }
};

/**
 * @brief 端口属性
 *
 * 包含端口的完整属性、状态和统计信息
 */
struct PortAttributes {
    // === 基础信息 ===
    SystemPortInfo   info;          // 端口系统信息
    PortCapabilities capabilities;  // 端口能力

    // === 状态信息 ===
    PortState       currentState;     // 当前端口状态
    ConnectionState connectionState;  // 连接状态
    QDateTime       stateChangeTime;  // 状态变更时间
    QString         lastError;        // 最后错误信息
    int             errorCode = 0;    // 错误代码

    // === 配置信息 ===
    QVariantMap configuration;  // 端口配置参数
    QVariantMap metadata;       // 元数据

    // === 使用信息 ===
    bool        isInUse = false;   // 是否正在使用
    QString     currentUser;       // 当前使用者
    QStringList connectedDevices;  // 连接的设备列表

    // === 统计信息 ===
    PortStatistics statistics;  // 端口统计

    // === 监控配置 ===
    bool        enableMonitoring   = true;  // 启用监控
    int         monitoringInterval = 1000;  // 监控间隔(ms)
    QStringList monitoringEvents;           // 监控事件列表

    /**
     * @brief 构造函数
     */
    PortAttributes() : currentState(PortState::Unavailable), connectionState(ConnectionState::Disconnected), stateChangeTime(QDateTime::currentDateTime()) {
    }

    /**
     * @brief 检查端口是否可用
     */
    bool isAvailable() const {
        return currentState == PortState::Available && !isInUse;
    }

    /**
     * @brief 检查端口是否健康
     */
    bool isHealthy() const {
        return currentState != PortState::Error && connectionState != ConnectionState::Error && lastError.isEmpty();
    }

    /**
     * @brief 检查是否支持指定设备
     */
    bool supportsDevice(const QString &deviceId) const {
        // 可以根据设备类型和端口能力进行匹配
        Q_UNUSED(deviceId)
        return true;  // 默认支持所有设备
    }
};

/**
 * @brief 端口事件信息
 */
struct PortEventInfo {
    QString     portId;        // 端口ID
    QString     eventType;     // 事件类型
    QString     eventMessage;  // 事件消息
    QDateTime   eventTime;     // 事件时间
    QVariantMap eventData;     // 事件数据

    PortEventInfo() : eventTime(QDateTime::currentDateTime()) {
    }
};

/**
 * @brief 端口发现配置
 */
struct PortDiscoveryConfig {
    bool        enableAutoDiscovery = true;  // 启用自动发现
    int         discoveryInterval   = 5000;  // 发现间隔(ms)
    QStringList includeTypes;                // 包含的端口类型
    QStringList excludeTypes;                // 排除的端口类型
    QStringList includePatterns;             // 包含的名称模式
    QStringList excludePatterns;             // 排除的名称模式

    bool deepScan    = false;  // 深度扫描
    int  maxScanTime = 10000;  // 最大扫描时间(ms)

    /**
     * @brief 检查端口是否应该被包含
     */
    bool shouldIncludePort(const SystemPortInfo &port) const;
};

/**
 * @brief 端口属性工具类
 */
class PortAttributesUtils {
  public:
    /**
     * @brief 端口类型转字符串
     */
    static QString portTypeToString(PortType type);

    /**
     * @brief 字符串转端口类型
     */
    static PortType stringToPortType(const QString &typeStr);

    /**
     * @brief 端口状态转字符串
     */
    static QString portStateToString(PortState state);

    /**
     * @brief 连接状态转字符串
     */
    static QString connectionStateToString(ConnectionState state);

    /**
     * @brief 验证端口属性
     */
    static bool validatePortAttributes(const PortAttributes &port);

    /**
     * @brief 比较两个端口属性
     */
    static bool comparePortAttributes(const PortAttributes &port1, const PortAttributes &port2);

    /**
     * @brief 从系统信息创建端口属性
     */
    static PortAttributes fromSystemInfo(const QVariantMap &sysInfo);

    /**
     * @brief 获取端口类型的默认能力
     */
    static PortCapabilities getDefaultCapabilities(PortType type);
};

}  // namespace DataStructures
}  // namespace Communication
}  // namespace LA