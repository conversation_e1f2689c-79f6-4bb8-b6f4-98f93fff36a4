```mermaid
graph TB
    subgraph "自动匹配核心流程"
        START[开始匹配]
        
        subgraph "第一阶段：扫描发现"
            PS[PortScanner<br/>端口扫描器]
            PL[端口列表<br/>串口/网口/USB等]
            DR[DeviceRegistry<br/>获取设备类型注册表]
            DT[设备类型列表<br/>SPRM/Motor/Sensor等]
        end
        
        subgraph "第二阶段：探测识别"
            LOOP[轮询每个端口]
            DP[DeviceProber<br/>设备探测器]
            CMD[发送探测指令<br/>设备信息查询]
            RESP[接收响应数据]
            DI[DeviceIdentifier<br/>设备识别器]
            TYPE[识别设备类型]
        end
        
        subgraph "第三阶段：匹配计算"
            MA[MatchingAlgorithm<br/>匹配算法]
            CALC[计算最优匹配]
            RESULT[匹配结果列表]
        end
        
        subgraph "第四阶段：注册管理"
            RM[RegistrationManager<br/>注册管理器]
            REG_DEV[注册设备实例]
            REG_PORT[注册端口实例]
            BIND[绑定生命周期]
            COMPLETE[匹配完成]
        end
    end
    
    subgraph "协调与依赖"
        MC[MatchingCoordinator<br/>匹配协调器<br/>统一协调各阶段]
        UCS[UnifiedCommandSystem<br/>提供探测指令]
        PM[PortManager<br/>端口管理]
        DS[DeviceSystem<br/>设备系统]
    end
    
    %% 主流程
    START --> PS
    PS --> PL
    START --> DR
    DR --> DT
    
    PL --> LOOP
    DT --> LOOP
    LOOP --> DP
    DP --> CMD
    CMD --> RESP
    RESP --> DI
    DI --> TYPE
    
    TYPE --> MA
    MA --> CALC
    CALC --> RESULT
    
    RESULT --> RM
    RM --> REG_DEV
    RM --> REG_PORT
    REG_DEV --> BIND
    REG_PORT --> BIND
    BIND --> COMPLETE
    
    %% 协调关系
    MC -.-> PS
    MC -.-> DP
    MC -.-> DI
    MC -.-> MA
    MC -.-> RM
    
    %% 依赖关系
    DP --> UCS
    DP --> PM
    RM --> DS
    RM --> PM
    
    classDef scanner fill:#e3f2fd
    classDef prober fill:#f3e5f5
    classDef identifier fill:#e8f5e8
    classDef algorithm fill:#fff2cc
    classDef coordinator fill:#ffebee
    classDef registration fill:#f1f8e9
    
    class PS,PL scanner
    class DP,CMD,RESP prober
    class DI,TYPE identifier
    class MA,CALC,RESULT algorithm
    class MC coordinator
    class RM,REG_DEV,REG_PORT,BIND registration
```