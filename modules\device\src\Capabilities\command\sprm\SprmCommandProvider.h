/**
 * @file SprmCommandProvider.h
 * @brief SPRM设备指令提供者 - 完整型号支持与标准协议实现
 *
 * 基于Linus原则设计：
 * - "Do One Thing": 只负责SPRM协议指令的生成和解析
 * - "Good Taste": 统一处理所有SPRM型号，无特殊情况
 * - 配置驱动：基于nova_proj_cmd.xml标准数据
 *
 * 支持型号：
 * - nova-A1    (标准版本)
 * - nova-A1B   (Bester协议版本)
 * - nova-A1D   (高级版本)
 * - nova-A1H   (高精度版本)
 * - nova-A1L   (低功耗版本)
 */

#pragma once

#include "../ICommandProvider.h"
#include <QByteArray>
#include <QMap>
#include <QVariantMap>

namespace LA::Device::Command {

/**
 * @brief SPRM协议指令映射结构
 */
struct SprmCommandMapping {
    QString     commandId;      // 如 "eCALIB1"
    QByteArray  standardBytes;  // 如 "55 5a 85 01 00 86"
    QString     description;    // 指令描述
    QStringList parameters;     // 支持的参数
};

/**
 * @brief SPRM型号配置
 */
struct SprmModelConfig {
    QString                           modelName;       // 如 "nova-A1"
    QMap<QString, SprmCommandMapping> commands;        // 指令映射表
    QVariantMap                       protocolConfig;  // 协议配置
};

/**
 * @brief SPRM指令提供者实现
 *
 * 核心特性：
 * - 🎯 统一指令接口：所有SPRM型号使用相同的API
 * - 📋 配置驱动：从标准XML数据加载指令定义
 * - 🔍 完整型号支持：覆盖所有生产环境使用的SPRM型号
 * - ✅ 字节码精确匹配：与nova_proj_cmd.xml完全一致
 */
class SprmCommandProvider : public ICommandProvider {
    Q_OBJECT

  public:
    explicit SprmCommandProvider(const QString &modelName = "nova-A1", QObject *parent = nullptr);
    virtual ~SprmCommandProvider() = default;

    // === 核心指令接口实现 ===

    QByteArray    generateCommand(const QString &commandId, const QVariantMap &params = {}) override;
    CommandResult parseResponse(const QByteArray &responseData) override;
    bool          validateCommand(const QString &commandId, const QVariantMap &params = {}) override;

    // === 指令定义查询实现 ===

    QStringList       getSupportedCommands() const override;
    CommandDefinition getCommandDefinition(const QString &commandId) const override;
    bool              supportsCommand(const QString &commandId) const override;

    // === 配置管理实现 ===

    bool        loadConfiguration(const QVariantMap &configData) override;
    QVariantMap getProviderInfo() const override;

    // === SPRM特定接口 ===

    /**
     * @brief 获取支持的SPRM型号列表
     * @return 型号列表
     */
    static QStringList getSupportedModels();

    /**
     * @brief 设置当前使用的SPRM型号
     * @param modelName 型号名称
     * @return 是否成功
     */
    bool setModel(const QString &modelName);

    /**
     * @brief 获取当前型号
     * @return 当前型号名称
     */
    QString getCurrentModel() const {
        return m_currentModel;
    }

    /**
     * @brief 从十六进制字符串解析字节数组
     * @param hexString 如 "55 5a 85 01 00 86"
     * @return 字节数组
     */
    static QByteArray parseHexString(const QString &hexString);

    /**
     * @brief 字节数组转换为十六进制字符串
     * @param data 字节数组
     * @return 十六进制字符串
     */
    static QString toHexString(const QByteArray &data);

  private:
    QString                        m_currentModel;
    QMap<QString, SprmModelConfig> m_modelConfigs;

    // === 初始化方法 ===

    /**
     * @brief 初始化所有SPRM型号的标准指令映射
     * 基于config/sprm_devices.json配置文件
     */
    void initializeStandardCommands();

    /**
     * @brief 从配置文件加载SPRM配置
     * @param configPath 配置文件路径
     * @return 是否加载成功
     */
    bool loadSprmConfigFromFile(const QString &configPath);

    /**
     * @brief 初始化后备指令配置
     * 当配置文件不可用时使用
     */
    void initializeFallbackCommands();

    /**
     * @brief 初始化特定型号的配置
     * @param modelName 型号名称
     * @return 型号配置
     */
    SprmModelConfig createModelConfig(const QString &modelName);

    /**
     * @brief 验证指令字节码的完整性
     * @param commandId 指令ID
     * @param expectedBytes 期望的字节序列
     * @return 是否有效
     */
    bool validateCommandBytes(const QString &commandId, const QByteArray &expectedBytes);

    /**
     * @brief 计算SPRM协议校验和
     * @param data 数据字节
     * @return 校验和
     */
    uint8_t calculateSprmChecksum(const QByteArray &data);

    // === 指令生成方法（Linus原则：三层优先级） ===

    /**
     * @brief 从XML标准数据加载指令（优先级1）
     * @param modelName 设备型号
     * @param commandCode 指令码
     * @return 标准字节序列，失败返回空
     */
    QByteArray loadCommandFromXmlStandard(const QString &modelName, int commandCode);

    /**
     * @brief 通过协议接口生成指令（优先级2）
     * @param commandCode 指令码
     * @param deviceConfig 设备配置
     * @return 协议生成的字节序列，失败返回空
     */
    QByteArray generateCommandViaProtocolInterface(int commandCode, const QVariantMap &deviceConfig);

    /**
     * @brief 算法生成指令（优先级3，后备方案）
     * @param commandCode 指令码
     * @param protocol 协议类型
     * @return 算法生成的字节序列
     */
    QByteArray generateCommandByAlgorithm(int commandCode, const QString &protocol);

    /**
     * @brief 处理指令参数
     * @param baseCommand 基础指令
     * @param params 参数映射
     * @return 处理后的指令
     */
    QByteArray processCommandParameters(const QByteArray &baseCommand, const QVariantMap &params);

    // === 响应解析方法 ===

    /**
     * @brief 解析SPRM简单校验和响应
     * @param responseData 响应数据
     * @return 解析结果
     */
    CommandResult parseSprmSimpleChecksumResponse(const QByteArray &responseData);

    /**
     * @brief 解析SPRM XOR响应
     * @param responseData 响应数据
     * @return 解析结果
     */
    CommandResult parseSprmXorResponse(const QByteArray &responseData);

    /**
     * @brief 解析SPRM Bester响应
     * @param responseData 响应数据
     * @return 解析结果
     */
    CommandResult parseSprmBesterResponse(const QByteArray &responseData);
};

}  // namespace LA::Device::Command
