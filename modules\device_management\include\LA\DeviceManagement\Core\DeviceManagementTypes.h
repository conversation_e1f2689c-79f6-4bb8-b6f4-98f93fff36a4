#pragma once

#include <QDateTime>
#include <QString>
#include <QStringList>
#include <QVariant>
#include <functional>
#include <memory>

// Foundation层统一类型定义 - 遵循类型定义分层原则
#include "core/CommonTypes.h"

namespace LA {
namespace DeviceManagement {

/**
 * @brief 设备实例生命周期状态 - 模块专有类型
 */
enum class InstanceLifecycle {
    Created,      // 已创建，未初始化
    Initialized,  // 已初始化，未启动
    Started,      // 已启动，可工作
    Connected,    // 已连接，通信正常
    Error,        // 错误状态
    Stopped,      // 已停止
    Destroyed     // 已销毁
};

// 使用Foundation层的契约类型 - 遵循单一来源原则
using DeviceType       = LA::Foundation::Core::DeviceType;
using PortType         = LA::Foundation::Core::PortType;
using PortStatus       = LA::Foundation::Core::PortStatus;
using ConnectionStatus = LA::Foundation::Core::ConnectionStatus;
using ResultCode       = LA::Foundation::Core::ResultCode;
using ConfigParameters = LA::Foundation::Core::ConfigParameters;
using DeviceInfo       = LA::Foundation::Core::DeviceInfo;
using PortInfo         = LA::Foundation::Core::PortInfo;

// 移除重复的DeviceInstanceInfo定义，使用下面的完整定义

// 前向声明
class IDevice;

// 基础类型已在 LA::Device::Core 中定义，这里只定义设备管理特有的类型

/**
 * @brief 设备状态枚举
 */
enum class DeviceState {
    Unknown      = 0,  // 未知状态
    Initializing = 1,  // 初始化中
    Ready        = 2,  // 就绪
    Running      = 3,  // 运行中
    Paused       = 4,  // 暂停
    Stopping     = 5,  // 停止中
    Stopped      = 6,  // 已停止
    Error        = 7   // 错误状态
};

/**
 * @brief 健康状态枚举
 */
enum class HealthStatus {
    Unknown  = 0,  // 未知
    Healthy  = 1,  // 健康
    Warning  = 2,  // 警告
    Critical = 3,  // 危险
    Error    = 4   // 错误
};

/**
 * @brief 设备配置结构
 */
struct DeviceConfig {
    QString     deviceId;    // 设备ID
    QString     deviceName;  // 设备名称
    QString     deviceType;  // 设备类型
    QVariantMap parameters;  // 配置参数
    bool        autoStart;   // 自动启动
    int         timeout;     // 超时时间(ms)

    DeviceConfig() : autoStart(false), timeout(5000) {
    }
};

/**
 * @brief 设备注册配置
 */
struct DeviceRegistrationConfig {
    LA::Foundation::Core::DeviceInfo deviceInfo;        // 设备信息（替换DeviceTypeInfo）
    QString           libraryPath;       // 库路径（如果是插件）
    QString           factoryFunction;   // 工厂函数名
    bool              isBuiltIn;         // 是否内置
    QDateTime         registrationTime;  // 注册时间

    DeviceRegistrationConfig() : isBuiltIn(true) {
        registrationTime = QDateTime::currentDateTime();
    }
};

/**
 * @brief 设备实例信息
 */
struct DeviceInstanceInfo {
    QString      instanceId;      // 实例ID
    QString      deviceType;      // 设备类型
    QString      friendlyName;    // 友好名称
    DeviceConfig config;          // 设备配置
    QVariantMap  instanceConfig;  // 实例特定配置
    DeviceState  state;           // 当前状态
    HealthStatus health;          // 健康状态
    QDateTime    createdTime;     // 创建时间
    QDateTime    createTime;      // 创建时间(别名，兼容性)
    QDateTime    lastActiveTime;  // 最后活动时间
    QVariantMap  statistics;      // 统计信息
    QVariantMap  runtimeData;     // 运行时数据
    QString      lastError;       // 最后错误

    // 生命周期状态 (使用已定义的枚举)
    InstanceLifecycle lifecycle;  // 生命周期状态

    DeviceInstanceInfo()
        : state(DeviceState::Unknown),
          health(HealthStatus::Unknown),
          lifecycle(InstanceLifecycle::Created),
          createdTime(QDateTime::currentDateTime()),
          createTime(createdTime)  // 别名指向相同值
          ,
          lastActiveTime(QDateTime::currentDateTime()) {
        createdTime    = QDateTime::currentDateTime();
        lastActiveTime = QDateTime::currentDateTime();
    }
};

/**
 * @brief 设备监控配置
 */
struct MonitoringConfig {
    bool        enabled;              // 是否启用监控
    int         interval;             // 监控间隔(ms)
    int         healthCheckInterval;  // 健康检查间隔(ms)
    bool        autoRestart;          // 自动重启
    int         maxRestartAttempts;   // 最大重启尝试次数
    QStringList monitoredMetrics;     // 监控的指标
    QVariantMap thresholds;           // 阈值配置

    MonitoringConfig() : enabled(true), interval(1000), healthCheckInterval(10000), autoRestart(false), maxRestartAttempts(3) {
    }
};

/**
 * @brief 发现配置
 */
struct DiscoveryConfig {
    bool        autoDiscovery;       // 自动发现
    int         discoveryInterval;   // 发现间隔(ms)
    QStringList enabledDiscoverers;  // 启用的发现器
    QStringList deviceTypes;         // 要发现的设备类型
    QVariantMap discovererConfig;    // 发现器配置

    DiscoveryConfig() : autoDiscovery(true), discoveryInterval(5000) {
    }
};

/**
 * @brief 服务状态枚举
 */
enum class ServiceState {
    Stopped  = 0,  // 已停止
    Starting = 1,  // 启动中
    Running  = 2,  // 运行中
    Stopping = 3,  // 停止中
    Error    = 4,  // 错误状态
    Paused   = 5   // 暂停状态
};

/**
 * @brief 工厂配置
 */
struct FactoryConfig {
    bool        enableCaching;      // 启用缓存
    int         maxCacheSize;       // 最大缓存大小
    bool        validateOnCreate;   // 创建时验证
    bool        enableMetrics;      // 启用指标
    QVariantMap defaultParameters;  // 默认参数

    FactoryConfig() : enableCaching(true), maxCacheSize(100), validateOnCreate(true), enableMetrics(true) {
    }
};

// 基础类型的转换函数已在 LA::Device::Core 中定义

}  // namespace DeviceManagement
}  // namespace LA
