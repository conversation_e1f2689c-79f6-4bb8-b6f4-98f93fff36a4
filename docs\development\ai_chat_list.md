ai chat内容记录

1. 分析当前项目的文档输出系统，并给出优化建议，
1.1 创建脚本，将文档中的文件中 文件中迁移到 此处
1.2 整合lenAdjust 异常日志文档
1.3 自动更新输出文档usage(变更记录 + 功能说明)
1.4 markdown转html

---

当前项目是一个现代工业软件，需要对旧代码进行现代化重构。
1. 总需求文档在 development.md中，通过双链的方式关联其他文档，注意通过双链获取其他文档内容
2. 重构计划在 development_plan.md 文档，查看 [[代码重构guidelines]]
3. 当前项目已经重构了应用层的部分模块，发现有个问题：各个模块的UI部分，使用的style都不相同，不符合涉及原则。当前先重构 主题系统 [[themeSystem]]。先修改该模块的开发文档再修改代码, 其他模块的ui统一使用 主题系统模块中的风格

这是一个现代工业软件项目，包括代码重构和新模块开发，具体内容查看 development.md 和 development_plan.md,    ││   当前将各模块UI部分改成使用 主题系统模块后，进行编译存在问题，继续修复   

build info:
    "cmake.buildDirectory": "${workspaceFolder}/build/${buildType}",
    "cmake.configureOnOpen": true,
    "cmake.generator": "Ninja",
    "cmake.configureArgs": [
        "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"
    ],
    "cmake.configureSettings": {
        "CMAKE_BUILD_TYPE": "${buildType}",
        "CMAKE_PREFIX_PATH": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64",
        "CMAKE_C_COMPILER": "D:/Programs/Qt/Qt5.14.2/Tools/mingw730_64/bin/gcc.exe",
        "CMAKE_CXX_COMPILER": "D:/Programs/Qt/Qt5.14.2/Tools/mingw730_64/bin/g++.exe"
    },

Q:
这是一个现代工业软件项目，包括代码重构和新模块开发，具体内容查看 development.md 和 development_plan.md。
1. 当前重构了 主题系统模块，其他模块和窗口都需要使用这个模块中的 style。
2. 解决软件UI的一些问题，从图片中可以看出：
2.1 侧边栏ICon存在被遮挡问题
2.2 setting模块 窗口内容显示存在问题
2.3 整体的UI不够美化、风格不够统一、最好是统一用简约圆润风格

修改前，先更新对应模块的开发文档，再修改代码

Q: 
这是一个现代工业软件项目，包括代码重构和新模块开发，具体内容查看 development.md 和 development_plan.md。
1. 从 基础设施层 和 support层开始开发和重构；
2. 从 底层、依赖少的模块开始
3. 这些模块单独编译，主程序先不添加这些模块所在的文件夹

修改前，先查看当前模块的开发文，分析档架是否合理，再修改代码

————————————

@docs\development\DOCUMENTATION_STRUCTURE_GUIDE.md @docs\development\guidelines\development_guideline.md @docs\development\development.md 
  当前项目设备经过重构，需要重新评估数据流功能是否正常。@docs\development\architecture\data_flow_architecture.md
  1. 端口和设备类型的发现和UI显示是否正常
2. 每个端口的自动匹配的架构是否正常？(可以先实现骨架，具体的设备指令内容，可以后续实现)
3. 更新 @docs\development\MASTER_DEVELOPMENT_PLAN.md的相关任务，添加数据流相关模块的完成状态\  

——————————————————
@docs\development\guidelines\development_guideline.md \                                     ││   @docs\development\DOCUMENTATION_STRUCTURE_GUI  ││   DE.md \                                        ││   @docs\development\development.md \             ││   按照 @docs\development\architecture\data_flow_architecture.md                               ││   中，设备端口自动匹配机制的架构图，             ││   @modules\device_management\ 存在问题\          ││   1. linus式原则 当前该模块放在                  ││   @modules\device_management\src\Matching\       ││   中实现，是否合理？\                            ││   2. @docs\development\development.md            ││   更新模块列表中的开发文档\                      ││    | 设备端口匹配器                          |   ││   模块层   | 匹配服务      |                     ││   `modules/device_management/Matching/\          ││   3. @modules\device_management\src\Discovery\D  ││   eviceDiscoveryService.cpp 中怎么有端口相关的   ││   代码，例如scanSerialDevices函数？单一职责呢？

---
3. 深度思考，设备模块需要在设备的构造函数中调用指令生成函数，保存到 可执行程序所在的目录中吗？  
3.1 保存时，先查看本地是否有该名称指令，有则跳过  
3.2 保存和加载使用xml格式，使用support中的模块 @development.md /模块列表。support中要有这个模块，如果没有，需要创建现代工业软件 文档生成和加载模块。