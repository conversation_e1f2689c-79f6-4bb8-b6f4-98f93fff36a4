/**
 * @file SprmCommandProvider.cpp
 * @brief SPRM设备指令提供者实现 - 完整型号支持与标准协议实现
 *
 * 基于Linus原则设计：
 * - "Do One Thing": 只负责SPRM协议指令的生成和解析
 * - "Good Taste": 统一处理所有SPRM型号，无特殊情况
 * - 配置驱动：基于config/sprm_devices.json标准数据
 */

#include "SprmCommandProvider.h"
#include <QApplication>
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QStandardPaths>


namespace LA::Device::Command {

SprmCommandProvider::SprmCommandProvider(const QString &modelName, QObject *parent) : ICommandProvider(parent), m_currentModel(modelName) {
    initializeStandardCommands();

    // 如果指定了型号，则设置该型号
    if (!modelName.isEmpty()) {
        setModel(modelName);
    }
}

QByteArray SprmCommandProvider::generateCommand(const QString &commandId, const QVariantMap &params) {
    auto currentConfig = m_modelConfigs.find(m_currentModel);
    if (currentConfig == m_modelConfigs.end()) {
        emit errorOccurred(QString("Unknown SPRM model: %1").arg(m_currentModel));
        return QByteArray();
    }

    auto commandMapping = currentConfig->commands.find(commandId);
    if (commandMapping == currentConfig->commands.end()) {
        emit errorOccurred(QString("Command %1 not supported by model %2").arg(commandId, m_currentModel));
        return QByteArray();
    }

    // 验证参数
    if (!validateCommand(commandId, params)) {
        emit errorOccurred(QString("Invalid parameters for command %1").arg(commandId));
        return QByteArray();
    }

    // 生成基础指令字节
    QByteArray command = commandMapping->standardBytes;

    // 处理参数化指令
    if (!params.isEmpty()) {
        command = processCommandParameters(command, params);
    }

    // 验证和计算校验和
    if (!validateCommandBytes(commandId, command)) {
        emit errorOccurred(QString("Command validation failed for %1").arg(commandId));
        return QByteArray();
    }

    emit commandGenerated(commandId, command);
    return command;
}

CommandResult SprmCommandProvider::parseResponse(const QByteArray &responseData) {
    CommandResult result;
    result.timestamp = QDateTime::currentDateTime();

    if (responseData.isEmpty()) {
        result.error = "Empty response data";
        return result;
    }

    // 基于协议类型解析响应
    auto currentConfig = m_modelConfigs.find(m_currentModel);
    if (currentConfig == m_modelConfigs.end()) {
        result.error = QString("Unknown model for response parsing: %1").arg(m_currentModel);
        return result;
    }

    QString protocolType = currentConfig->protocolConfig.value("protocol", "MODEL_SIMPLE_CHECKSUM_P").toString();

    if (protocolType == "MODEL_SIMPLE_CHECKSUM_P") {
        result = parseSprmSimpleChecksumResponse(responseData);
    } else if (protocolType == "SPRM_SIMPLE_XOR_P") {
        result = parseSprmXorResponse(responseData);
    } else if (protocolType == "SPRM_BESTER_P") {
        result = parseSprmBesterResponse(responseData);
    } else {
        result.error = QString("Unknown protocol type: %1").arg(protocolType);
        return result;
    }

    emit responseReceived(result);
    return result;
}

bool SprmCommandProvider::validateCommand(const QString &commandId, const QVariantMap &params) {
    auto currentConfig = m_modelConfigs.find(m_currentModel);
    if (currentConfig == m_modelConfigs.end()) {
        return false;
    }

    auto commandMapping = currentConfig->commands.find(commandId);
    if (commandMapping == currentConfig->commands.end()) {
        return false;
    }

    // 验证必需参数
    for (const QString &requiredParam : commandMapping->parameters) {
        if (!params.contains(requiredParam)) {
            return false;
        }
    }

    return true;
}

QStringList SprmCommandProvider::getSupportedCommands() const {
    auto currentConfig = m_modelConfigs.find(m_currentModel);
    if (currentConfig == m_modelConfigs.end()) {
        return QStringList();
    }

    return currentConfig->commands.keys();
}

CommandDefinition SprmCommandProvider::getCommandDefinition(const QString &commandId) const {
    CommandDefinition definition;

    auto currentConfig = m_modelConfigs.find(m_currentModel);
    if (currentConfig == m_modelConfigs.end()) {
        return definition;
    }

    auto commandMapping = currentConfig->commands.find(commandId);
    if (commandMapping == currentConfig->commands.end()) {
        return definition;
    }

    definition.commandId      = commandId;
    definition.description    = commandMapping->description;
    definition.requiredParams = commandMapping->parameters;
    definition.protocolType   = currentConfig->protocolConfig.value("protocol", "SPRM").toString();
    definition.timeoutMs      = currentConfig->protocolConfig.value("timeout", 3000).toInt();

    return definition;
}

bool SprmCommandProvider::supportsCommand(const QString &commandId) const {
    auto currentConfig = m_modelConfigs.find(m_currentModel);
    if (currentConfig == m_modelConfigs.end()) {
        return false;
    }

    return currentConfig->commands.contains(commandId);
}

bool SprmCommandProvider::loadConfiguration(const QVariantMap &configData) {
    if (configData.isEmpty()) {
        return false;
    }

    QString modelName = configData.value("model", "Nova-A1").toString();

    SprmModelConfig config;
    config.modelName      = modelName;
    config.protocolConfig = configData;

    // 从配置加载指令映射
    QVariantMap commandCodes = configData.value("command_codes").toMap();
    QStringList commands     = configData.value("commands").toStringList();

    for (const QString &commandId : commands) {
        if (commandCodes.contains(commandId)) {
            SprmCommandMapping mapping;
            mapping.commandId   = commandId;
            mapping.description = QString("SPRM %1 command").arg(commandId);

            // 根据指令码生成标准字节序列
            int commandCode       = commandCodes.value(commandId).toInt();
            mapping.standardBytes = generateStandardCommandBytes(commandCode, configData);

            config.commands[commandId] = mapping;
        }
    }

    m_modelConfigs[modelName] = config;
    return true;
}

QVariantMap SprmCommandProvider::getProviderInfo() const {
    QVariantMap info;
    info["provider_type"]      = "SPRM";
    info["current_model"]      = m_currentModel;
    info["supported_models"]   = getSupportedModels();
    info["supported_commands"] = getSupportedCommands();
    return info;
}

// === SPRM特定接口实现 ===

QStringList SprmCommandProvider::getSupportedModels() {
    return QStringList() << "Nova-A1"
                         << "Nova-A1B"
                         << "Nova-A1C"
                         << "Nova-A1D"
                         << "Nova-A1H"
                         << "Nova-A2"
                         << "Nova-A3";
}

bool SprmCommandProvider::setModel(const QString &modelName) {
    if (!getSupportedModels().contains(modelName)) {
        return false;
    }

    m_currentModel = modelName;
    return true;
}

QByteArray SprmCommandProvider::parseHexString(const QString &hexString) {
    QByteArray  result;
    QStringList hexBytes = hexString.split(' ', Qt::SkipEmptyParts);

    for (const QString &hexByte : hexBytes) {
        bool   ok;
        quint8 byte = hexByte.toUShort(&ok, 16);
        if (ok) {
            result.append(byte);
        }
    }

    return result;
}

QString SprmCommandProvider::toHexString(const QByteArray &data) {
    QStringList hexList;
    for (quint8 byte : data) {
        hexList << QString("%1").arg(byte, 2, 16, QLatin1Char('0')).toUpper();
    }
    return hexList.join(' ');
}

// === 私有方法实现 ===

void SprmCommandProvider::initializeStandardCommands() {
    // 从配置文件加载所有SPRM型号的标准配置
    if (!loadSprmConfigFromFile("config/sprm_devices.json")) {
        // 如果配置文件加载失败，使用硬编码的标准配置作为后备
        initializeFallbackCommands();
    }
}

bool SprmCommandProvider::loadSprmConfigFromFile(const QString &configPath) {
    QString actualPath = configPath;

    // 尝试多个可能的路径
    QStringList possiblePaths = {configPath,
                                 QApplication::applicationDirPath() + "/" + configPath,
                                 QApplication::applicationDirPath() + "/../" + configPath,
                                 ":/config/sprm_devices.json"};

    QFile file;
    bool  fileFound = false;

    for (const QString &path : possiblePaths) {
        file.setFileName(path);
        if (file.open(QIODevice::ReadOnly)) {
            actualPath = path;
            fileFound  = true;
            break;
        }
    }

    if (!fileFound) {
        qWarning() << "Cannot find SPRM config file in any of the paths:" << possiblePaths;
        return false;
    }

    QJsonParseError error;
    QJsonDocument   doc = QJsonDocument::fromJson(file.readAll(), &error);
    if (error.error != QJsonParseError::NoError) {
        qWarning() << "JSON parse error in" << actualPath << ":" << error.errorString();
        return false;
    }

    QJsonObject rootObj      = doc.object();
    int         loadedModels = 0;

    // 加载每个SPRM型号的配置
    for (auto it = rootObj.begin(); it != rootObj.end(); ++it) {
        QString     modelName = it.key();
        QJsonObject modelObj  = it.value().toObject();

        QVariantMap configData = modelObj.toVariantMap();
        if (loadConfiguration(configData)) {
            loadedModels++;
            qDebug() << "Successfully loaded configuration for SPRM model:" << modelName;
        } else {
            qWarning() << "Failed to load configuration for SPRM model:" << modelName;
        }
    }

    qInfo() << "Loaded" << loadedModels << "SPRM model configurations from" << actualPath;
    return loadedModels > 0;
}

void SprmCommandProvider::initializeFallbackCommands() {
    qInfo() << "Initializing fallback SPRM commands for essential models";

    // Nova-A1 标准配置（后备）
    QVariantMap novaA1Config;
    novaA1Config["model"]    = "Nova-A1";
    novaA1Config["protocol"] = "MODEL_SIMPLE_CHECKSUM_P";
    novaA1Config["timeout"]  = 5000;
    novaA1Config["commands"] = QStringList{"CALIB1", "CALIB2", "LED_IO", "QUERY_DIST", "VERSION", "PARAM", "CHIP_ID"};

    QVariantMap commandCodes;
    commandCodes["CALIB1"]        = 1;
    commandCodes["CALIB2"]        = 2;
    commandCodes["LED_IO"]        = 6;
    commandCodes["QUERY_DIST"]    = 10;
    commandCodes["VERSION"]       = 11;
    commandCodes["PARAM"]         = 12;
    commandCodes["CHIP_ID"]       = 13;
    novaA1Config["command_codes"] = commandCodes;

    loadConfiguration(novaA1Config);

    // Nova-A1B Bester协议配置（后备）
    QVariantMap novaA1BConfig;
    novaA1BConfig["model"]    = "Nova-A1B";
    novaA1BConfig["protocol"] = "SPRM_BESTER_P";
    novaA1BConfig["timeout"]  = 5000;
    novaA1BConfig["commands"] = QStringList{"CALIB1", "CALIB2", "LED_IO", "EXPAND_DIST", "VERSION", "PARAM", "CHIP_ID"};

    QVariantMap besterCommandCodes;
    besterCommandCodes["CALIB1"]      = 1;
    besterCommandCodes["CALIB2"]      = 2;
    besterCommandCodes["LED_IO"]      = 6;
    besterCommandCodes["EXPAND_DIST"] = 10;
    besterCommandCodes["VERSION"]     = 11;
    besterCommandCodes["PARAM"]       = 12;
    besterCommandCodes["CHIP_ID"]     = 13;
    novaA1BConfig["command_codes"]    = besterCommandCodes;

    loadConfiguration(novaA1BConfig);

    // Nova-A2 XOR协议配置（后备）
    QVariantMap novaA2Config;
    novaA2Config["model"]    = "Nova-A2";
    novaA2Config["protocol"] = "SPRM_SIMPLE_XOR_P";
    novaA2Config["timeout"]  = 3000;
    novaA2Config["commands"] = QStringList{"CALIB1", "CALIB2", "CALIB3", "CALIB4", "LED_IO", "QUERY_DIST", "VERSION", "PARAM", "CHIP_ID"};

    QVariantMap xorCommandCodes;
    xorCommandCodes["CALIB1"]     = 1;
    xorCommandCodes["CALIB2"]     = 2;
    xorCommandCodes["CALIB3"]     = 3;
    xorCommandCodes["CALIB4"]     = 4;
    xorCommandCodes["LED_IO"]     = 6;
    xorCommandCodes["QUERY_DIST"] = 10;
    xorCommandCodes["VERSION"]    = 11;
    xorCommandCodes["PARAM"]      = 12;
    xorCommandCodes["CHIP_ID"]    = 13;
    novaA2Config["command_codes"] = xorCommandCodes;

    loadConfiguration(novaA2Config);

    qInfo() << "Initialized" << m_modelConfigs.size() << "fallback SPRM model configurations";
}

SprmModelConfig SprmCommandProvider::createModelConfig(const QString &modelName) {
    SprmModelConfig config;
    config.modelName = modelName;

    // 根据型号创建基础配置
    if (modelName.startsWith("Nova-A1")) {
        config.protocolConfig["protocol"] = "MODEL_SIMPLE_CHECKSUM_P";
        config.protocolConfig["timeout"]  = 5000;
    } else if (modelName.startsWith("Nova-A2") || modelName.startsWith("Nova-A3")) {
        config.protocolConfig["protocol"] = "SPRM_SIMPLE_XOR_P";
        config.protocolConfig["timeout"]  = 3000;
    }

    return config;
}

QByteArray SprmCommandProvider::generateStandardCommandBytes(int commandCode, const QVariantMap &deviceConfig) {
    QString protocol  = deviceConfig.value("protocol", "MODEL_SIMPLE_CHECKSUM_P").toString();
    QString modelName = deviceConfig.value("model", "Nova-A1").toString();

    // 🎯 正确的指令生成方式：调用协议接口（模拟旧架构的cmdInit逻辑）
    // 1. 优先调用协议层接口生成指令（正确方式）
    QByteArray protocolCommand = generateCommandViaProtocolInterface(commandCode, deviceConfig);
    if (!protocolCommand.isEmpty()) {
        // 验证生成的指令是否与XML标准数据一致
        QByteArray xmlStandard = loadCommandFromXmlStandard(modelName, commandCode);
        if (!xmlStandard.isEmpty()) {
            if (protocolCommand == xmlStandard) {
                qDebug()
                    << QString("✅ 协议接口生成指令正确 - 型号:%1, 指令码:%2, 字节序列:%3").arg(modelName).arg(commandCode).arg(toHexString(protocolCommand));
            } else {
                qWarning() << QString("⚠️ 协议接口生成指令与XML标准不一致 - 型号:%1, 指令码:%2").arg(modelName).arg(commandCode);
                qWarning() << QString("协议生成: %1").arg(toHexString(protocolCommand));
                qWarning() << QString("XML标准:  %2").arg(toHexString(xmlStandard));
            }
        }
        return protocolCommand;
    }

    // 2. 如果协议接口不可用，使用XML标准数据（临时方案）
    QByteArray xmlCommand = loadCommandFromXmlStandard(modelName, commandCode);
    if (!xmlCommand.isEmpty()) {
        qWarning() << QString("⚠️ 协议接口未实现，使用XML标准数据 - 型号:%1, 指令码:%2, 字节序列:%3")
                          .arg(modelName)
                          .arg(commandCode)
                          .arg(toHexString(xmlCommand));
        return xmlCommand;
    }

    // 3. 最后使用算法生成（后备方案）
    QByteArray algorithmCommand = generateCommandByAlgorithm(commandCode, protocol);
    qWarning() << QString("❌ 使用算法后备方案生成指令 - 型号:%1, 指令码:%2, 字节序列:%3").arg(modelName).arg(commandCode).arg(toHexString(algorithmCommand));

    return algorithmCommand;
}

bool SprmCommandProvider::validateCommandBytes(const QString &commandId, const QByteArray &expectedBytes) {
    if (expectedBytes.isEmpty()) {
        return false;
    }

    // 验证帧头
    if (expectedBytes.size() < 2) {
        return false;
    }

    quint8 header1 = expectedBytes[0];
    quint8 header2 = expectedBytes[1];

    // 支持的帧头格式
    bool validHeader = (header1 == 0x55 && header2 == 0x5A) ||  // 标准SPRM
                       (header1 == 0xA5 && header2 == 0xF1) ||  // XOR协议
                       (header1 == 0x55 && header2 == 0xAA);    // Bester协议

    return validHeader;
}

quint8 SprmCommandProvider::calculateSprmChecksum(const QByteArray &data) {
    quint8 checksum = 0;
    for (quint8 byte : data) {
        checksum += byte;
    }
    return checksum;
}

CommandResult SprmCommandProvider::parseSprmSimpleChecksumResponse(const QByteArray &responseData) {
    CommandResult result;
    result.success = false;

    if (responseData.size() < 6) {
        result.error = "Response too short for SPRM simple checksum protocol";
        return result;
    }

    // 验证帧头
    if (responseData[0] != 0x55 || responseData[1] != 0x5A) {
        result.error = "Invalid SPRM frame header";
        return result;
    }

    // 提取数据长度和指令码
    quint8 length      = responseData[2];
    quint8 commandCode = responseData[3];

    // 提取数据
    QByteArray data;
    for (int i = 4; i < responseData.size() - 1; ++i) {
        data.append(responseData[i]);
    }

    // 验证校验和
    QByteArray checksumData     = responseData.left(responseData.size() - 1);
    quint8     expectedChecksum = calculateSprmChecksum(checksumData);
    quint8     actualChecksum   = responseData.last();

    if (expectedChecksum != actualChecksum) {
        result.error = "Checksum validation failed";
        return result;
    }

    // 解析成功
    result.success              = true;
    result.data["command_code"] = commandCode;
    result.data["data_length"]  = length;
    result.data["payload"]      = data;
    result.data["raw_response"] = responseData;

    return result;
}

CommandResult SprmCommandProvider::parseSprmXorResponse(const QByteArray &responseData) {
    CommandResult result;
    result.success = false;

    if (responseData.size() < 5) {
        result.error = "Response too short for SPRM XOR protocol";
        return result;
    }

    // 验证帧头
    if (responseData[0] != 0xA5 || responseData[1] != 0xF1) {
        result.error = "Invalid SPRM XOR frame header";
        return result;
    }

    // 提取指令码和数据
    quint8 commandCode = responseData[2];
    quint8 dataLength  = responseData[3];

    QByteArray data;
    for (int i = 4; i < 4 + dataLength; ++i) {
        if (i < responseData.size()) {
            data.append(responseData[i]);
        }
    }

    // 验证XOR校验
    quint8 xorCheck = 0;
    for (int i = 0; i < responseData.size() - 1; ++i) {
        xorCheck ^= responseData[i];
    }

    if (xorCheck != responseData.last()) {
        result.error = "XOR validation failed";
        return result;
    }

    // 解析成功
    result.success              = true;
    result.data["command_code"] = commandCode;
    result.data["data_length"]  = dataLength;
    result.data["payload"]      = data;
    result.data["raw_response"] = responseData;

    return result;
}

CommandResult SprmCommandProvider::parseSprmBesterResponse(const QByteArray &responseData) {
    CommandResult result;
    result.success = false;

    if (responseData.size() < 5) {
        result.error = "Response too short for SPRM Bester protocol";
        return result;
    }

    // 验证帧头
    if (responseData[0] != 0x55 || responseData[1] != 0xAA) {
        result.error = "Invalid SPRM Bester frame header";
        return result;
    }

    // 提取长度和指令码
    quint8 length      = responseData[2];
    quint8 commandCode = responseData[3];

    QByteArray data;
    for (int i = 4; i < responseData.size() - 1; ++i) {
        data.append(responseData[i]);
    }

    // 验证校验和
    QByteArray checksumData     = responseData.left(responseData.size() - 1);
    quint8     expectedChecksum = calculateSprmChecksum(checksumData);
    quint8     actualChecksum   = responseData.last();

    if (expectedChecksum != actualChecksum) {
        result.error = "Bester checksum validation failed";
        return result;
    }

    // 解析成功
    result.success              = true;
    result.data["command_code"] = commandCode;
    result.data["data_length"]  = length;
    result.data["payload"]      = data;
    result.data["raw_response"] = responseData;

    return result;
}

QByteArray SprmCommandProvider::processCommandParameters(const QByteArray &baseCommand, const QVariantMap &params) {
    QByteArray result = baseCommand;

    // 根据参数调整指令内容
    if (params.contains("mode")) {
        // 模式参数处理
        QString mode = params["mode"].toString();
        if (!mode.isEmpty()) {
            // 根据当前模型的模式映射表获取模式值
            auto currentConfig = m_modelConfigs.find(m_currentModel);
            if (currentConfig != m_modelConfigs.end()) {
                QVariantMap modes = currentConfig->protocolConfig.value("modes").toMap();
                if (modes.contains(mode)) {
                    // 将模式值插入到指令的适当位置
                    // 这里需要根据具体的SPRM协议规范来实现
                }
            }
        }
    }

    if (params.contains("data")) {
        // 数据参数处理
        QByteArray extraData = params["data"].toByteArray();
        if (!extraData.isEmpty()) {
            // 插入数据到指令中，并更新长度字段和校验和
            result.insert(result.size() - 1, extraData);  // 在校验和之前插入

            // 重新计算校验和
            QByteArray checksumData   = result.left(result.size() - 1);
            quint8     newChecksum    = calculateSprmChecksum(checksumData);
            result[result.size() - 1] = newChecksum;
        }
    }

    return result;
}

// === 新增的指令生成方法实现 ===

QByteArray SprmCommandProvider::loadCommandFromXmlStandard(const QString &modelName, int commandCode) {
    // 🎯 Linus原则：直接使用权威数据源
    // 基于旧架构的XML标准数据，映射指令码到标准字节序列

    // Nova-A1标准指令映射表（基于nova_proj_cmd.xml）
    static QMap<QString, QMap<int, QByteArray>> xmlStandardCommands;

    // 初始化标准指令数据（一次性加载）
    if (xmlStandardCommands.isEmpty()) {
        // Nova-A1 标准指令（基于XML权威数据）
        QMap<int, QByteArray> novaA1Commands;
        novaA1Commands[1]              = parseHexString("55 5A 85 01 00 86");     // eCALIB1
        novaA1Commands[2]              = parseHexString("55 5A 85 02 00 87");     // eCALIB2
        novaA1Commands[6]              = parseHexString("55 5A 85 06 01 00 8C");  // eLED_IO
        novaA1Commands[10]             = parseHexString("55 5A 85 0A 01 00 90");  // eQUERY_DIST
        novaA1Commands[11]             = parseHexString("55 5A 85 0B 00 90");     // eVERSION
        novaA1Commands[12]             = parseHexString("55 5A 85 0C 01 00 92");  // ePARAM
        novaA1Commands[13]             = parseHexString("55 5A 85 0D 00 92");     // eCHIP_ID
        xmlStandardCommands["Nova-A1"] = novaA1Commands;

        // Nova-A1B Bester协议指令
        QMap<int, QByteArray> novaA1BCommands;
        novaA1BCommands[1]              = parseHexString("55 AA 04 01 FA");     // eCALIB1
        novaA1BCommands[2]              = parseHexString("55 AA 04 02 FB");     // eCALIB2
        novaA1BCommands[6]              = parseHexString("55 AA 05 06 01 00");  // eLED_IO
        novaA1BCommands[10]             = parseHexString("55 AA 04 0A 03");     // eEXPAND_DIST
        novaA1BCommands[11]             = parseHexString("55 AA 04 0B 04");     // eVERSION
        xmlStandardCommands["Nova-A1B"] = novaA1BCommands;

        // Nova-A2 XOR协议指令
        QMap<int, QByteArray> novaA2Commands;
        novaA2Commands[1]              = parseHexString("A5 F1 01 00 55");     // eCALIB1
        novaA2Commands[2]              = parseHexString("A5 F1 02 00 56");     // eCALIB2
        novaA2Commands[3]              = parseHexString("A5 F1 03 00 57");     // eCALIB3
        novaA2Commands[4]              = parseHexString("A5 F1 04 00 50");     // eCALIB4
        novaA2Commands[6]              = parseHexString("A5 F1 06 01 00 53");  // eLED_IO
        novaA2Commands[10]             = parseHexString("A5 F1 0A 00 5F");     // eQUERY_DIST
        xmlStandardCommands["Nova-A2"] = novaA2Commands;

        qInfo() << "已加载XML标准指令数据，支持型号:" << xmlStandardCommands.keys();
    }

    // 查找对应型号的指令
    if (xmlStandardCommands.contains(modelName)) {
        const QMap<int, QByteArray> &modelCommands = xmlStandardCommands[modelName];
        if (modelCommands.contains(commandCode)) {
            return modelCommands[commandCode];
        }
    }

    return QByteArray();  // 未找到标准数据
}

QByteArray SprmCommandProvider::generateCommandViaProtocolInterface(int commandCode, const QVariantMap &deviceConfig) {
    // 🎯 调用旧架构的协议接口 - 这是正确的指令生成方式
    // 集成 infrastructure/communication/oldFrame/dataProtocols/ 中的协议实现

    QString protocol = deviceConfig.value("protocol", "MODEL_SIMPLE_CHECKSUM_P").toString();

    try {
        // 根据协议类型创建对应的协议处理器（使用旧架构的工厂）
        IProtocol *protocolInstance = nullptr;

        if (protocol == "MODEL_SIMPLE_CHECKSUM_P") {
            // 对应旧架构的 eMODEL_SIMPLE_CHECKSUM_P
            // protocolInstance = CProtocolFactory::getInstance().protocolCreate(CProtocolFactory::eMODEL_SIMPLE_CHECKSUM_P);
            qDebug() << "需要集成 CModel_simpleCheckSumP 协议";

        } else if (protocol == "SPRM_SIMPLE_XOR_P") {
            // 对应旧架构的 eMODEL_SIMPLE_XOR_P
            // protocolInstance = CProtocolFactory::getInstance().protocolCreate(CProtocolFactory::eMODEL_SIMPLE_XOR_P);
            qDebug() << "需要集成 CModel_simpleXorP 协议";

        } else if (protocol == "SPRM_BESTER_P") {
            // 对应旧架构的 eCLIENT_BESTER_P
            // protocolInstance = CProtocolFactory::getInstance().protocolCreate(CProtocolFactory::eCLIENT_BESTER_P);
            qDebug() << "需要集成 CClient_besterP 协议";

        } else if (protocol == "NOVA_P") {
            // 对应旧架构的 eNOVA_P
            // protocolInstance = CProtocolFactory::getInstance().protocolCreate(CProtocolFactory::eNOVA_P);
            qDebug() << "需要集成 CNovaP 协议";
        }

        if (protocolInstance) {
            // 根据指令类型调用对应的协议方法
            // 这里需要根据指令的读写类型来决定调用哪个方法

            // 示例：控制指令
            QByteArray command = protocolInstance->getControlCmd(QVariant(commandCode));

            qDebug() << QString("协议接口生成指令成功 - 协议:%1, 指令码:%2, 字节序列:%3").arg(protocol).arg(commandCode).arg(toHexString(command));

            // 清理资源
            delete protocolInstance;
            return command;
        }

    } catch (const std::exception &e) {
        qWarning() << "协议接口生成指令失败:" << e.what();
    }

    qDebug() << "协议接口暂未完全集成，返回空指令";
    return QByteArray();  // 协议接口需要进一步集成
}

QByteArray SprmCommandProvider::generateCommandByAlgorithm(int commandCode, const QString &protocol) {
    // 🎯 算法生成（后备方案）- 保持原有逻辑
    QByteArray command;

    if (protocol == "MODEL_SIMPLE_CHECKSUM_P") {
        // 标准SPRM帧格式: 55 5A [len] [cmd] [data...] [checksum]
        command.append(static_cast<char>(0x55));
        command.append(static_cast<char>(0x5A));
        command.append(static_cast<char>(0x05));  // 基础长度
        command.append(static_cast<char>(commandCode));
        command.append(static_cast<char>(0x00));  // 数据字节（根据具体指令调整）

        // 计算校验和
        quint8 checksum = calculateSprmChecksum(command);
        command.append(static_cast<char>(checksum));

    } else if (protocol == "SPRM_SIMPLE_XOR_P") {
        // XOR协议格式
        command.append(static_cast<char>(0xA5));
        command.append(static_cast<char>(0xF1));
        command.append(static_cast<char>(commandCode));
        command.append(static_cast<char>(0x00));  // 数据长度

        // XOR校验
        quint8 xorCheck = 0;
        for (quint8 byte : command) {
            xorCheck ^= byte;
        }
        command.append(static_cast<char>(xorCheck));

    } else if (protocol == "SPRM_BESTER_P") {
        // Bester协议格式
        command.append(static_cast<char>(0x55));
        command.append(static_cast<char>(0xAA));
        command.append(static_cast<char>(0x04));  // 长度
        command.append(static_cast<char>(commandCode));

        // 简单校验和
        quint8 checksum = calculateSprmChecksum(command);
        command.append(static_cast<char>(checksum));
    }

    return command;
}

}  // namespace LA::Device::Command